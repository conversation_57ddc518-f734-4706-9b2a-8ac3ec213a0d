#!/bin/bash

# This script synchronizes the 'install' directory and the 'test_run.sh' 
# file from the local machine to a remote server at ************:/oem/workstation_bt/
# It clears the known hosts record for ************ before initiating the sync.

USER=$(whoami)
ssh-keygen -f "/home/<USER>/.ssh/known_hosts" -R "************"
rsync -avz install droid@************:/oem/workstation_bt/
scp test_run.sh droid@************:/oem/workstation_bt/