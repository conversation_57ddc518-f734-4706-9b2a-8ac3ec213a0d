#!/bin/sh

mkdir install
curl https://artifactory.gz.cvte.cn/artifactory/robot/packages/common/arm64/dev/common_arm64_1.0.0.729.dev.tar -o install/common_install.tar && cd install && tar -xvf common_install.tar && rm common_install.tar && cd ../
curl https://artifactory.gz.cvte.cn/artifactory/robot/packages/cvte_interfaces/arm64/release/cvte_interfaces_arm64_1.1.0.108.release.tar -o install/cvte_interfaces_install.tar && cd install && tar -xvf cvte_interfaces_install.tar && rm cvte_interfaces_install.tar && cd ../
curl https://artifactory.gz.cvte.cn/artifactory/robot/packages/zbus_cpp/arm64/release/zbus_cpp_arm64_1.0.0.147.release.tar -o install/zbus_cpp_install.tar && cd install && tar -xvf zbus_cpp_install.tar && rm zbus_cpp_install.tar && cd ../
curl https://artifactory.gz.cvte.cn/artifactory/robot/packages/common_packages/arm64/release/common_packages_arm64_1.0.0.40.release.tar -o install/common_packages_install.tar && cd install && tar -xvf common_packages_install.tar && rm common_packages_install.tar && cd ../