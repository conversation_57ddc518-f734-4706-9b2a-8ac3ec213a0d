#!/bin/sh

mkdir install
curl https://artifactory.gz.cvte.cn/artifactory/robot/packages/common/amd64/dev/common_amd64_1.0.0.333.dev.tar -o install/common_install.tar && cd install && tar -xvf common_install.tar && rm common_install.tar && cd ../
curl https://artifactory.gz.cvte.cn/artifactory/robot/packages/cvte_interfaces/amd64/release/cvte_interfaces_amd64_1.1.0.73.release.tar -o install/cvte_interfaces_install.tar && cd install && tar -xvf cvte_interfaces_install.tar && rm cvte_interfaces_install.tar && cd ../
curl https://artifactory.gz.cvte.cn/artifactory/robot/packages/zbus_cpp/amd64/release/zbus_cpp_amd64_1.0.0.54.release.tar -o install/zbus_cpp_install.tar && cd install && tar -xvf zbus_cpp_install.tar && rm zbus_cpp_install.tar && cd ../