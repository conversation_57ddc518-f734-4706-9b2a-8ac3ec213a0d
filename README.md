# workstation_bt
工作站流程行为树代码
## 系统依赖
```bash
sudo apt -y install libyaml-cpp-dev libgoogle-glog-dev
```
## 应用依赖
```bash
#arm64
./build_depend_install_arm64.sh
#amd64
./build_depend_install_amd64.sh
```
## 测试编译
```bash
./test_build.sh
```
## 测试运行
```bash
./test_run.sh
```
## 调试方法
1.将修改好的代码推上git，分支以.feature结尾，然后在artifactory上拉deb包安装调试
2.在本地通过交叉编译，或mac虚拟机编译得到可执行文件，先暂停机器的行为树进程，然后通过`./scp_install`脚本拷贝文件到机器（机器不需要联网，但可能比较慢），或者通过`robot-script store install`上传文件到服务器，然后从服务器下载解压（机器需要联网，速度比较快）