/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-06-28 22:18:26
 * @LastEditors: cheng<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-08-05 16:31:07
 * @FilePath:
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置
 * 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include "bt_cout_logger.h"

namespace BT {

StdCoutLogger::StdCoutLogger(const BT::Tree &tree)
    : StatusChangeLogger(tree.rootNode()) {}
StdCoutLogger::~StdCoutLogger() {}

void StdCoutLogger::callback(Duration timestamp, const TreeNode &node,
                             NodeStatus prev_status, NodeStatus status) {
    (void) timestamp;

    // 节点与类型名相同的节点状态改变不打印
    // 关键节点应当修改节点名,以便确定到底是图中哪一个节点
    if (node.name() == node.registrationName() || node.name().empty()) {
        // LOG(INFO) << "name == class name";
        return;
    }

    // 不打印恢复空闲状态的日志
    if (status == NodeStatus::IDLE) {
        return;
    }

    // 不打印从RUNNING到RUNNING的日志
    if (prev_status == NodeStatus::RUNNING && status == NodeStatus::RUNNING) {
        return;
    }

    // 不打印子树运行情况
    if (node.type() == NodeType::SUBTREE) {
        return;
    }

    using namespace std::chrono;

    constexpr const char * whitespaces = "                         ";
    constexpr const size_t ws_count    = 25;

    /*double since_epoch = duration<double>(timestamp).count();
    printf("[%.3f]: %s%s %s -> %s", since_epoch, node.name().c_str(),
           &whitespaces[std::min(ws_count, node.name().size())],
           toStr(prev_status, true).c_str(), toStr(status, true).c_str());
    std::cout << std::endl;*/
    LOG(WARNING) << node.name()
                 << &whitespaces[std::min(ws_count, node.name().size())]
                 << toStr(prev_status, false) << " -> " << toStr(status, false);
}

void StdCoutLogger::flush() {
    // std::cout << std::flush;
}

}  // namespace BT
