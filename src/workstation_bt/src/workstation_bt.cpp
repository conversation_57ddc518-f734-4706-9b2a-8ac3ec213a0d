#include "workstation_bt.hpp"

#define MIN_TURN_TO_SEC       60
#define ADD_LIQUID_TIMEOUT    42
#define EXTRA_ADD_LIQUID_TIME 2

static bool isEqual(float num1, float num2, float epsilon = 1e-2) {
    return fabs(num1 - num2) < epsilon;
}

class Service_call : public StatefulActionNode {
   public:
    Service_call(const std::string &name, const NodeConfiguration &config,
                 std::vector<service_call_map> &arg1, ZbusNode::SharedPtr &arg2)
        : StatefulActionNode(name, config),
          service_call(arg1),
          node_ptr(arg2) {}

    NodeStatus onStart() override {
        if (getInput<std::string>("service_name").value() == "NULL") {
            return NodeStatus::SUCCESS;
        }

        for (size_t i = 0; i < service_call.size(); i++) {
            if (service_call[i].name ==
                getInput<std::string>("service_name").value()) {
                LOG(INFO) << "client exist: " << service_call[i].name;
                return NodeStatus::RUNNING;
            }
        }

        service_call_map service;
        service.name = getInput<std::string>("service_name").value();
        service.ptr  = node_ptr->create_client(service.name);
        service_call.push_back(service);
        LOG(INFO) << "register new client: " << service.name;

        return NodeStatus::RUNNING;
    }

    NodeStatus onRunning() override {
        int index = service_call_index();

        if (index < 0) {
            return NodeStatus::FAILURE;
        }

        std_srvs::srv::SetBool::Request  request;
        std_srvs::srv::SetBool::Response result;

        request.data = getInput<std::float_t>("data").value();
        LOG(INFO) << service_call[index].name << " data: " << request.data;

        auto ret =
            service_call[index].ptr->async_send_request<std_srvs::srv::SetBool>(
                request, result, seconds(1));

        if (!ret) {
            LOG(INFO) << getInput<std::string>("service_name").value()
                      << " service return error !!!";
            return BT::NodeStatus::FAILURE;
        }

        return NodeStatus::SUCCESS;
    }

    void onHalted() override {
        std::cout << "Service_call interrupted" << std::endl;
    }

    static PortsList providedPorts() {
        return {InputPort<std::string>("service_name"),
                InputPort<std::float_t>("data")};
    }

   private:
    int service_call_index(void) {
        for (size_t i = 0; i < service_call.size(); i++) {
            if (service_call[i].name ==
                getInput<std::string>("service_name").value()) {
                return i;
            }
        }
        LOG(ERROR) << "vector broken: "
                   << getInput<std::string>("service_name").value();
        return -1;
    }

   private:
    std::vector<service_call_map> &service_call;
    ZbusNode::SharedPtr &          node_ptr;
};

class Start_charge : public SyncActionNode {
   public:
    Start_charge(const std::string &name, const NodeConfiguration &config,
                 ZbusPublisher::SharedPtr &arg)
        : SyncActionNode(name, config),
          auto_charge_start(arg),
          input_data(getInput<std::string>("data").value()) {}
    NodeStatus tick() override {
        static CppTime::Timer timer;
        static std::size_t    id;
        static bool           timer_started = false;

        if (!timer_started && input_data == "false") {
            timer_started = true;
            id            = timer.add(
                seconds(0),
                [=](CppTime::timer_id) {
                    std_msgs::msg::Bool msg;
                    msg.data = true;
                    auto_charge_start->publish(msg);
                },
                seconds(1));

        } else if (timer_started && input_data == "true") {
            timer_started = false;
            timer.remove(id);
        }

        return NodeStatus::SUCCESS;
    }
    static PortsList providedPorts() {
        return {InputPort<std::string>("data")};
    }

   private:
    ZbusPublisher::SharedPtr &auto_charge_start;
    std::string               input_data;
};

template <size_t NUM_CASES>
class SetBlackboardN : public SyncActionNode {
   public:
    SetBlackboardN(const std::string &name, const NodeConfiguration &config)
        : SyncActionNode(name, config) {
        setRegistrationID("SetBlackboardN");
    }

    static PortsList providedPorts() {
        PortsList ports;

        for (unsigned i = 0; i < NUM_CASES; i++) {
            ports.insert(BT::InputPort("value_" + std::to_string(i + 1)));
            ports.insert(
                BT::BidirectionalPort("output_key_" + std::to_string(i + 1)));
        }
        return ports;
    }

   private:
    virtual BT::NodeStatus tick() override {
        for (unsigned i = 0; i < NUM_CASES; i++) {
            std::string key, value;
            std::string input_port  = "value_" + std::to_string(i + 1);
            std::string output_port = "output_key_" + std::to_string(i + 1);
            if (!getInput(output_port, key)) {
                throw RuntimeError("missing port " + output_port);
            }
            if (!getInput(input_port, value)) {
                throw RuntimeError("missing port " + input_port);
            }
            setOutput(output_port, value);
        }
        return NodeStatus::SUCCESS;
    }
};

class Get_process_type : public SyncActionNode {
   public:
    Get_process_type(const std::string &name, const NodeConfiguration &config,
                     robot_state &arg)
        : SyncActionNode(name, config), robot(arg) {}

    NodeStatus tick() override {
        if (robot.is_workstation_start) {
            if (robot.water_supply_status == true) {
                LOG(INFO) << "执行状态：任务中补给";
                setOutput("process", "DuringTaskSupply");
                return NodeStatus::SUCCESS;
            }
            if (robot.is_manual_ctrl_start) {
                LOG(INFO) << "执行状态：手动补给";
                setOutput("process", "ManualSupply");
                return NodeStatus::SUCCESS;
            }
            LOG(INFO) << "执行状态：全流程补给";
            setOutput("process", "WholeProcessSupply");
            return NodeStatus::SUCCESS;
        }
        setOutput("process", "NULL");
        return NodeStatus::FAILURE;
    }
    static PortsList providedPorts() {
        return {OutputPort<std::string>("process")};
    }

   private:
    robot_state &robot;
};

class Process_type_finish : public SyncActionNode {
   public:
    Process_type_finish(const std::string &      name,
                        const NodeConfiguration &config, robot_state &arg)
        : SyncActionNode(name, config), robot(arg) {}
    NodeStatus tick() override {
        if (getInput<std::string>("type").value() == "water_supply") {
            LOG(INFO) << "Water_Supply Success!";
            robot.water_supply_status = false;
            return NodeStatus::SUCCESS;
        } else {
            return NodeStatus::FAILURE;
        }
    }
    static PortsList providedPorts() {
        return {InputPort<std::string>("type")};
    }

   private:
    robot_state &robot;
};

class CalcAddCleanFluidTime : public SyncActionNode {
   public:
    CalcAddCleanFluidTime(const std::string &      name,
                          const NodeConfiguration &config, robot_state &arg)
        : SyncActionNode(name, config), robot(arg) {}

    NodeStatus tick() override {
        auto cleanCapacityResult = getInput<std::uint32_t>("clean_capacity");
        auto cleanFluidTrafficResult =
            getInput<std::uint32_t>("clean_fluid_traffic");
        auto cleanFluidRatioResult = getInput<float>("clean_fluid_ratio");

        if (!cleanCapacityResult)
            throw RuntimeError("Missing port [clean_capacity]: " +
                               cleanCapacityResult.error());
        if (!cleanFluidTrafficResult)
            throw RuntimeError("Missing port [clean_fluid_traffic]: " +
                               cleanFluidTrafficResult.error());
        if (!cleanFluidRatioResult)
            throw RuntimeError("Missing port [clean_fluid_ratio]: " +
                               cleanFluidRatioResult.error());

        uint32_t clean_capacity      = cleanCapacityResult.value();
        uint32_t clean_fluid_traffic = cleanFluidTrafficResult.value();
        float    liquid_ratio        = cleanFluidRatioResult.value();

        robot.clean_fluid_ratio = liquid_ratio;

        if (isEqual(liquid_ratio, 0.0f)) {
            LOG(INFO) << "不需要加清洁液";
            setOutput("clean_fluid", "0");
            return NodeStatus::FAILURE;
        }

        if (robot.add_full_water_time == 0)
            throw RuntimeError("robot.add_full_water_time cannot be zero");

        float liquid_flow_rate =
            static_cast<float>(clean_fluid_traffic) / MIN_TURN_TO_SEC;
        float clean_water_flow_rate =
            static_cast<float>(clean_capacity) / robot.add_full_water_time;

        // Calculate the required fluid volume (ml).
        float fluid_volume =
            liquid_ratio * clean_water_flow_rate * robot.delta_add_water_time;
        // Calculate the required time (seconds) and round the result.
        int clean_fluid_time =
            static_cast<int>(std::round(fluid_volume / liquid_flow_rate));

        clean_fluid_time += EXTRA_ADD_LIQUID_TIME;
        if (clean_fluid_time > ADD_LIQUID_TIMEOUT)
            clean_fluid_time = ADD_LIQUID_TIMEOUT;

        LOG(INFO) << "清水平均流速为： " << clean_water_flow_rate << "ml/s, "
                  << "清洁液需要增加： " << fluid_volume << "ml, "
                  << "清洁液配比为： " << liquid_ratio << ", "
                  << "清洁液流速为： " << liquid_flow_rate << "ml/s";
        LOG(INFO) << "需要加清洁液 " << clean_fluid_time << "秒";

        setOutput("clean_fluid", std::to_string(clean_fluid_time));
        return NodeStatus::SUCCESS;
    }

    static PortsList providedPorts() {
        return {InputPort<std::uint32_t>("clean_capacity"),
                InputPort<std::uint32_t>("clean_fluid_traffic"),
                InputPort<float>("clean_fluid_ratio"),
                OutputPort<std::string>("clean_fluid")};
    }

   private:
    robot_state &robot;
};

class Force_set_clean_fluid : public SyncActionNode {
   public:
    Force_set_clean_fluid(const std::string &      name,
                          const NodeConfiguration &config, robot_state &arg)
        : SyncActionNode(name, config), robot(arg) {}

    NodeStatus tick() override {
        auto ratioResult = getInput<float>("clean_fluid_ratio");
        if (!ratioResult) {
            throw RuntimeError("Missing port [clean_fluid_ratio]: " +
                               ratioResult.error());
        }
        float liquid_ratio = ratioResult.value();

        robot.clean_fluid_ratio = liquid_ratio;
        robot.delta_clean_water_volume =
            robot.clean_water_volume - robot.last_clean_water_volume;

        // 根据公式计算添加清洁液所需时间
        // 清洁液平均流速为 5 ml/s，本体水箱容量为 4000 ml
        int clean_fluid_time =
            static_cast<int>(((4000.0f * robot.delta_clean_water_volume) *
                              robot.clean_fluid_ratio) /
                             5.0f);
        clean_fluid_time += EXTRA_ADD_LIQUID_TIME;

        setOutput("clean_fluid", std::to_string(clean_fluid_time));
        LOG(INFO) << "添加清洁液的时间为：" << clean_fluid_time;
        return NodeStatus::SUCCESS;
    }

    static PortsList providedPorts() {
        return {InputPort<float>("clean_fluid_ratio"),
                OutputPort<std::string>("clean_fluid")};
    }

   private:
    robot_state &robot;
};

class Process_return : public StatefulActionNode {
   public:
    Process_return(const std::string &name, const NodeConfiguration &config)
        : StatefulActionNode(name, config) {}

    NodeStatus onStart() override { return NodeStatus::RUNNING; }

    NodeStatus onRunning() override {
        if (getInput<std::string>("ret").value() == "running") {
            return NodeStatus::RUNNING;
        } else {
            LOG(INFO) << getInput<std::string>("ret").value();
            return NodeStatus::SUCCESS;
        }
    }

    void onHalted() override {
        std::cout << "Process_return interrupted" << std::endl;
    }

    static PortsList providedPorts() {
        return {InputPort<std::string>("behavior"),
                InputPort<std::string>("ret")};
    }
};

bool Workstation_Bt::checkMember(const nlohmann::json &root,
                                 const std::string &key, std::string &reason,
                                 nlohmann::json::value_t type) {
    using json_t = nlohmann::json::value_t;
    if (!root.contains(key) || root[key].type() != type) {
        reason = "参数" + key + "类型错误";
        return false;
    }

    if (json_t::string == type && root[key].get<std::string>().empty()) {
        reason = "参数(string)" + key + "为空";
        return false;
    }

    if (json_t::array == type && root[key].empty()) {
        reason = "参数(array)" + key + "为空";
        return false;
    }

    if (json_t::object == type && root[key].empty()) {
        reason = "参数(object)" + key + "为空";
        return false;
    }

    return true;
}

void Workstation_Bt::workstationManualCtrlCallback(
    const nlohmann::json &request, nlohmann::json &respond) {
    std::string           reason;
    std_msgs::msg::String msg;
    // 拒绝优先级：急停 > 不在桩 > 参数异常 > 执行其它任务 > 已执行

    if (robot.is_emerg) {
        respond["code"] = ManualRespondCode::EMERGENCY;
        respond["msg"]  = "急停状态，拒绝执行";
        LOG(ERROR) << "MANUAL：急停状态，拒绝执行";
        msg.data = "manual_emerg";
        workstation_task_pub_ptr_->publish(msg);
        return;
    }

    if (!robot.is_in_pile) {
        respond["code"] = ManualRespondCode::NOT_IN_PILE;
        respond["msg"]  = "机器不在桩，拒绝执行";
        LOG(ERROR) << "MANUAL：机器不在桩，拒绝执行";
        msg.data = "manual_not_in_pile";
        workstation_task_pub_ptr_->publish(msg);
        return;
    }

    if (!checkMember(request, "manual_ctrl_name", reason,
                     nlohmann::json::value_t::string) ||
        !checkMember(request, "open", reason,
                     nlohmann::json::value_t::boolean)) {
        respond["code"] = ManualRespondCode::PARAM_ERROR;
        respond["msg"]  = reason;
        LOG(ERROR) << "MANUAL：参数异常，拒绝执行";
        msg.data = "manual_error_param";
        workstation_task_pub_ptr_->publish(msg);
        return;
    }

    if (robot.is_workstation_start) {
        respond["code"] = ManualRespondCode::SUPPLYING;
        respond["msg"]  = "正在进行补给，拒绝执行";
        LOG(ERROR) << "MANUAL：正在进行补给，拒绝执行";
        msg.data = "manual_supplying";
        workstation_task_pub_ptr_->publish(msg);
        return;
    }

    std::string manual_ctrl_name = request["manual_ctrl_name"];
    bool        open             = request["open"];

    if (manual_ctrl_name.empty()) {
        respond["code"] = ManualRespondCode::PARAM_ERROR;
        respond["msg"]  = "参数无法匹配，拒绝执行";
        LOG(ERROR) << "MANUAL：参数无法匹配，拒绝执行";
        msg.data = "manual_error_param";
        workstation_task_pub_ptr_->publish(msg);
        return;
    }

    // 如果 manual_ctrl_name 不在 manual_ctrl_list 中，添加默认状态
    if (robot.manual_ctrl_list.find(manual_ctrl_name) ==
        robot.manual_ctrl_list.end()) {
        robot.manual_ctrl_list[manual_ctrl_name] = false;
    }

    // 检查当前状态是否与上一次相同
    auto it = robot.manual_ctrl_list.find(manual_ctrl_name);
    if (it != robot.manual_ctrl_list.end() && it->second == open) {
        respond["code"] = ManualRespondCode::STATE_UNCHANGED;
        respond["msg"]  = "状态未改变，拒绝执行";
        LOG(ERROR) << "MANUAL：状态未改变，拒绝执行";
        msg.data = "manual_no_changed";
        workstation_task_pub_ptr_->publish(msg);
        return;
    }

    // 检查是否有其他控制操作正在进行
    for (const auto &entry : robot.manual_ctrl_list) {
        if (entry.second == true && entry.first != manual_ctrl_name) {
            respond["code"] = ManualRespondCode::OTHER_CTRL_RUNNING;
            respond["msg"]  = "其他控制操作正在进行，拒绝执行";
            LOG(ERROR) << "MANUAL：其他控制操作正在进行，拒绝执行";
            msg.data = "manual_other_ctrl";
            workstation_task_pub_ptr_->publish(msg);
            return;
        }
    }
    if (manual_ctrl_name == "manual_add_water" &&
        isEqual(robot.clean_water_volume, 1.0)) {
        respond["code"] = ManualRespondCode::WATER_FULL;
        respond["msg"]  = "清水箱水量已满，拒绝执行";
        msg.data        = "manual_water_full";
        workstation_task_pub_ptr_->publish(msg);
        return;

    } else if (manual_ctrl_name == "manual_drain_water" &&
               isEqual(robot.clean_water_volume, 0.0)) {
        respond["code"] = ManualRespondCode::WATER_EMPTY;
        respond["msg"]  = "清水箱水量为空，拒绝执行";
        msg.data        = "manual_water_empty";
        workstation_task_pub_ptr_->publish(msg);
        return;
    }
    robot.manual_ctrl_list[manual_ctrl_name] = open;
    if (open) {
        robot.is_manual_ctrl_start = true;
        robot.is_manual_ctrl_abort = false;
        robot.is_workstation_start = true;
    } else if (!open) {
        robot.is_manual_ctrl_abort = true;
        robot.is_workstation_start = false;
    }

    respond["code"] = ManualRespondCode::SUCCESS;
    respond["msg"]  = "执行成功";
    LOG(INFO) << "MANUAL：执行成功";
}

class manual_ctrl_finish : public SyncActionNode {
   public:
    manual_ctrl_finish(const std::string &name, const NodeConfiguration &config,
                       robot_state &arg)
        : SyncActionNode(name, config), robot(arg) {}

    NodeStatus tick() override {
        std::string manual_ctrl_name =
            getInput<std::string>("ctrl_name").value();
        auto it = robot.manual_ctrl_list.find(manual_ctrl_name);
        if (it != robot.manual_ctrl_list.end()) {
            it->second = false;
            LOG(INFO) << "MANUAL:" << manual_ctrl_name << " FINISH";
        }
        robot.is_manual_ctrl_start = false;
        robot.is_manual_ctrl_abort = false;
        robot.is_workstation_start = false;
        return NodeStatus::SUCCESS;
    }

    static PortsList providedPorts() {
        return {InputPort<std::string>("ctrl_name")};
    }

   private:
    robot_state &robot;
};

class Get_manual_ctrl_process : public SyncActionNode {
   public:
    Get_manual_ctrl_process(const std::string &      name,
                            const NodeConfiguration &config, robot_state &arg)
        : SyncActionNode(name, config), robot(arg) {}

    NodeStatus tick() override {
        for (const auto &entry : robot.manual_ctrl_list) {
            if (entry.second) {
                LOG(INFO) << "MANUAL-->" << entry.first;
                setOutput("manual_ctrl_name", entry.first);
                setOutput("open", "true");
                return NodeStatus::SUCCESS;
            }
        }
        return NodeStatus::FAILURE;
    }
    static PortsList providedPorts() {
        return {OutputPort<std::string>("manual_ctrl_name"),
                OutputPort<std::string>("open")};
    }

   private:
    robot_state &robot;
};

class Update_Robot_State : public SyncActionNode {
   public:
    Update_Robot_State(const std::string &name, const NodeConfiguration &config,
                       ZbusPublisher::SharedPtr &arg1,
                       ZbusPublisher::SharedPtr &arg2,
                       ZbusPublisher::SharedPtr &arg3,
                       ZbusPublisher::SharedPtr &arg4)
        : SyncActionNode(name, config),
          water_position_update(arg1),
          charge_feedback_update(arg2),
          sewage_tank_pos_update(arg3),
          robot_dust_position_update(arg4) {}
    NodeStatus tick() override {
        LOG(INFO) << "获取机器状态 -- update";
        std_msgs::msg::Empty empty;
        water_position_update->publish(empty);
        charge_feedback_update->publish(empty);
        sewage_tank_pos_update->publish(empty);
        robot_dust_position_update->publish(empty);

        return NodeStatus::SUCCESS;
    }

   private:
    ZbusPublisher::SharedPtr &water_position_update;
    ZbusPublisher::SharedPtr &charge_feedback_update;
    ZbusPublisher::SharedPtr &sewage_tank_pos_update;
    ZbusPublisher::SharedPtr &robot_dust_position_update;
};

class Waiting_charge_state : public StatefulActionNode {
   public:
    Waiting_charge_state(const std::string &      name,
                         const NodeConfiguration &config, robot_state &arg)
        : StatefulActionNode(name, config), robot(arg) {}

    NodeStatus onStart() override { return NodeStatus::RUNNING; }

    NodeStatus onRunning() override {
        if (getInput<std::float_t>("percentage").value() != -1 &&
            getInput<std::float_t>("percentage").value() >
                robot.battery_percent * 100) {
            return NodeStatus::RUNNING;
        }

        if (getInput<std::float_t>("power_supply_status").value() != -1 &&
            getInput<std::float_t>("power_supply_status").value() !=
                robot.power_supply_status) {
            return NodeStatus::RUNNING;
        }

        if (getInput<std::string>("state").value() != robot.charge_state) {
            return NodeStatus::RUNNING;
        }

        LOG(INFO) << "电量达到: "
                  << getInput<std::float_t>("percentage").value()
                  << "，电池状态达到: "
                  << getInput<std::string>("power_supply_status").value()
                  << "，autocharge状态达到: "
                  << getInput<std::string>("state").value();

        robot.charge_state = "Idle";

        return NodeStatus::SUCCESS;
    }

    void onHalted() override {
        std::cout << "Waiting_charge_state interrupted" << std::endl;
    }

    static PortsList providedPorts() {
        return {InputPort<std::float_t>("percentage"),
                InputPort<std::float_t>("power_supply_status"),
                InputPort<std::string>("state")};
    }

   private:
    robot_state &robot;
};

class Get_clean_water_position : public SyncActionNode {
   public:
    Get_clean_water_position(const std::string &      name,
                             const NodeConfiguration &config, robot_state &arg)
        : SyncActionNode(name, config), robot(arg) {}

    NodeStatus tick() override {
        if (isEqual(robot.clean_water_volume, 0.0)) {
            robot.calibrate_add_water_time = true;
            LOG(INFO) << "开始校准加满清水时间";
        } else {
            robot.calibrate_add_water_time = false;
        }
        robot.last_clean_water_volume = robot.clean_water_volume;
        LOG(INFO) << "加水前清水量为: " << robot.last_clean_water_volume;
        setOutput("water_current_level", robot.clean_water_volume);
        return NodeStatus::SUCCESS;
    }

    static PortsList providedPorts() {
        return {OutputPort<std::float_t>("water_current_level")};
    }

   private:
    robot_state &robot;
};

class Is_clean_water_volume_change : public ConditionNode {
   public:
    Is_clean_water_volume_change(const std::string &      name,
                                 const NodeConfiguration &config,
                                 robot_state &            arg)
        : ConditionNode(name, config), robot(arg) {}
    NodeStatus tick() override {
        float last_water_level =
            getInput<std::float_t>("last_water_level").value();
        if (!isEqual(last_water_level, robot.clean_water_volume)) {
            LOG(INFO) << "清水量变化 加水前: " << last_water_level
                      << " 加水后: " << robot.clean_water_volume;
            setOutput("is_change", "true");
            return NodeStatus::SUCCESS;
        } else {
            LOG(INFO) << "清水量无变化 加水前: " << last_water_level
                      << " 加水后: " << robot.clean_water_volume;
            setOutput("is_change", "false");
            return NodeStatus::FAILURE;
        }
    }
    static PortsList providedPorts() {
        return {InputPort<std::float_t>("last_water_level"),
                OutputPort<std::string>("is_change")};
    }

   private:
    robot_state &robot;
};

class GetLastTaskType : public SyncActionNode {
   public:
    GetLastTaskType(const std::string &name, const NodeConfiguration &config,
                    robot_state &arg)
        : SyncActionNode(name, config), robot(arg) {}

    NodeStatus tick() override {
        std::string lastTaskType = "unKnownTask";

        try {
            if (!isEqual(robot.sewage_water_volume, 0.0)) {
                lastTaskType = "floor_washing";
            } else if (!isEqual(robot.dust_volume, 0.0)) {
                lastTaskType = "vacuuming";
            }
            setOutput("LastTaskType", lastTaskType);
            return NodeStatus::SUCCESS;
        } catch (const std::exception &e) {
            LOG(ERROR) << "Failed to get last task type: " << e.what();
            robot.is_workstation_start = false;
            return NodeStatus::FAILURE;
        }
    }

    static PortsList providedPorts() {
        return {OutputPort<std::string>("LastTaskType")};
    }

   private:
    robot_state &robot;
};

class Workstation_blackboard : public SyncActionNode {
   public:
    Workstation_blackboard(const std::string &      name,
                           const NodeConfiguration &config)
        : SyncActionNode(name, config) {}

    NodeStatus tick() override {
        std::string key1, value1;
        if (!getInput("output_key1", key1)) {
            throw RuntimeError("missing port [output_key1]");
        }
        if (!getInput("value1", value1)) {
            throw RuntimeError("missing port [value1]");
        }
        std::string key2, value2;
        if (!getInput("output_key2", key2)) {
            throw RuntimeError("missing port [output_key2]");
        }
        if (!getInput("value2", value2)) {
            throw RuntimeError("missing port [value2]");
        }
        std::string key3, value3;
        if (!getInput("output_key3", key3)) {
            throw RuntimeError("missing port [output_key3]");
        }
        if (!getInput("value3", value3)) {
            throw RuntimeError("missing port [value3]");
        }
        std::string key4, value4;
        if (!getInput("output_key4", key4)) {
            throw RuntimeError("missing port [output_key4]");
        }
        if (!getInput("value4", value4)) {
            throw RuntimeError("missing port [value4]");
        }
        setOutput("output_key1", value1);
        setOutput("output_key2", value2);
        setOutput("output_key3", value3);
        setOutput("output_key4", value4);
        return NodeStatus::SUCCESS;
    }
    static PortsList providedPorts() {
        return {InputPort("value1"),
                InputPort("value2"),
                InputPort("value3"),
                InputPort("value4"),
                BidirectionalPort("output_key1"),
                BidirectionalPort("output_key2"),
                BidirectionalPort("output_key3"),
                BidirectionalPort("output_key4")};
    }
};

class VoiceSwitchBlackboard : public SyncActionNode {
   public:
    VoiceSwitchBlackboard(const std::string &      name,
                          const NodeConfiguration &config)
        : SyncActionNode(name, config) {}

    NodeStatus tick() override {
        std::string mode = getInput<std::string>("ctrl_mode").value();
        std::string pub_start_voice, pub_end_voice;
        if (mode == "auto") {
            pub_start_voice =
                getInput<std::string>("auto_pub_start_voice").value();
            pub_end_voice = getInput<std::string>("auto_pub_end_voice").value();
            LOG(INFO) << "自动模式";
        } else if (mode == "manual") {
            pub_start_voice =
                getInput<std::string>("manual_pub_start_voice").value();
            pub_end_voice =
                getInput<std::string>("manual_pub_end_voice").value();
            LOG(INFO) << "手动模式";
        } else {
            LOG(WARNING) << "未知模式: " << mode;
        }
        setOutput("voice_pub_start", pub_start_voice);
        setOutput("voice_pub_end", pub_end_voice);
        return NodeStatus::SUCCESS;
    }
    static PortsList providedPorts() {
        return {InputPort<std::string>("ctrl_mode"),
                InputPort<std::string>("auto_pub_start_voice"),
                InputPort<std::string>("auto_pub_end_voice"),
                InputPort<std::string>("manual_pub_start_voice"),
                InputPort<std::string>("manual_pub_end_voice"),
                OutputPort<std::string>("voice_pub_end"),
                OutputPort<std::string>("voice_pub_start")};
    }
};

class PubStateLog : public DecoratorNode {
   public:
    PubStateLog(const std::string &name, const NodeConfiguration &config,
                ZbusPublisher::SharedPtr &arg1, ZbusPublisher::SharedPtr &arg2,
                ZbusPublisher::SharedPtr &arg3)
        : DecoratorNode(name, config),
          run_log(arg1),
          pipeline(arg2),
          float_log(arg3) {}

    NodeStatus tick() override {
        if (!child()) {
            throw RuntimeError("PubStateLog: missing child");
        }

        NodeStatus status = child()->executeTick();

        const std::string expect_val = getInput<std::string>("expect").value();
        const std::string status_str = nodeStatusToString(status);
        if (expect_val != status_str) {
            return status;
        }

        // Retrieve log parameters.
        const std::string application_name =
            getInput<std::string>("application_name").value();
        const std::string iot_log_type =
            getInput<std::string>("log_type").value();
        const std::string iot_log_value =
            getInput<std::string>("log_value").value();
        const std::string pipeline_log_type =
            getInput<std::string>("pipeline_log_type").value();
        const std::string pipeline_log_value =
            getInput<std::string>("pipeline_log_value").value();
        auto              float_log_type = getInput<bool>("float_log_type");
        const std::string float_log_value =
            getInput<std::string>("float_log_value").value();

        // Publish IoT log.
        if (!iot_log_type.empty() && !iot_log_value.empty()) {
            chassis_interfaces::msg::UploadRunLog log_msg;
            log_msg.log_type         = iot_log_type;
            log_msg.application_name = application_name;
            log_msg.log_value        = iot_log_value;
            run_log->publish(log_msg);
        }

        // Publish pipeline log.
        if (!pipeline_log_type.empty() && !pipeline_log_value.empty()) {
            chassis_interfaces::msg::UploadRunLog log_msg;
            log_msg.log_type  = pipeline_log_type;
            log_msg.log_value = pipeline_log_value;
            pipeline->publish(log_msg);
        }

        // Publish float log if provided.
        if (!float_log_value.empty() && float_log_type.has_value()) {
            nlohmann::json::object_t log_msg;
            log_msg["error_code"]   = float_log_value;
            log_msg["error_status"] = float_log_type.value();
            float_log->publish_json(log_msg);
        }

        return status;
    }

    static PortsList providedPorts() {
        return {InputPort<std::string>("application_name"),
                InputPort<std::string>("expect"),
                InputPort<bool>("float_log_type"),
                InputPort<std::string>("float_log_value"),
                InputPort<std::string>("log_type"),
                InputPort<std::string>("log_value"),
                InputPort<std::string>("pipeline_log_type"),
                InputPort<std::string>("pipeline_log_value")};
    }

   private:
    static std::string nodeStatusToString(NodeStatus status) {
        switch (status) {
            case NodeStatus::SUCCESS:
                return "SUCCESS";
            case NodeStatus::FAILURE:
                return "FAILURE";
            case NodeStatus::RUNNING:
                return "RUNNING";
            default:
                return "UNKNOWN";
        }
    }

   private:
    ZbusPublisher::SharedPtr &run_log;
    ZbusPublisher::SharedPtr &pipeline;
    ZbusPublisher::SharedPtr &float_log;
};

class Run_log_pub : public SyncActionNode {
   public:
    Run_log_pub(const std::string &name, const NodeConfiguration &config,
                ZbusPublisher::SharedPtr arg1, ZbusPublisher::SharedPtr arg2,
                ZbusPublisher::SharedPtr arg3)
        : SyncActionNode(name, config),
          run_log(arg1),
          pipeline_log(arg2),
          float_log(arg3) {}

    NodeStatus tick() override {
        std::string iot_log_application_name =
            getInput<std::string>("application_name").value();
        std::string iot_log_type  = getInput<std::string>("log_type").value();
        std::string iot_log_value = getInput<std::string>("log_value").value();

        std::string pipeline_log_type =
            getInput<std::string>("pipeline_log_type").value();
        std::string pipeline_log_value =
            getInput<std::string>("pipeline_log_value").value();

        auto        float_log_type = getInput<bool>("float_log_type");
        std::string float_log_value =
            getInput<std::string>("float_log_value").value();

        if (!iot_log_type.empty() && !iot_log_value.empty()) {
            chassis_interfaces::msg::UploadRunLog log_msg;
            log_msg.log_type         = iot_log_type;
            log_msg.application_name = iot_log_application_name;
            log_msg.log_value        = iot_log_value;
            run_log->publish(log_msg);
        }

        if (!pipeline_log_type.empty() && !pipeline_log_value.empty()) {
            chassis_interfaces::msg::UploadRunLog log_msg;
            log_msg.log_type  = pipeline_log_type;
            log_msg.log_value = pipeline_log_value;
            pipeline_log->publish(log_msg);
        }

        if (!float_log_value.empty() && float_log_type.has_value()) {
            nlohmann::json::object_t log_msg;
            log_msg["error_code"]   = float_log_value;
            log_msg["error_status"] = float_log_type.value();
            float_log->publish_json(log_msg);
        }

        return NodeStatus::SUCCESS;
    }

    static PortsList providedPorts() {
        return {InputPort<std::string>("application_name"),
                InputPort<std::string>("log_type"),
                InputPort<std::string>("log_value"),
                InputPort<std::string>("pipeline_log_type"),
                InputPort<std::string>("pipeline_log_value"),
                InputPort<bool>("float_log_type"),
                InputPort<std::string>("float_log_value")};
    }

   private:
    ZbusPublisher::SharedPtr run_log;
    ZbusPublisher::SharedPtr pipeline_log;
    ZbusPublisher::SharedPtr float_log;
};

class Single_Component_Ctrl : public AsyncActionNode {
   public:
    Single_Component_Ctrl(const std::string &       name,
                          const NodeConfiguration & config,
                          ZbusPublisher::SharedPtr &arg)
        : AsyncActionNode(name, config), component_ctl(arg) {}
    NodeStatus tick() override {
        std::string component_name =
            getInput<std::string>("component_name").value();
        int    control_gap_sec = getInput<int>("control_gap_sec").value();
        int    control_sec     = getInput<int>("control_sec").value();
        double level           = getInput<double>("level").value();
        steady_clock::time_point end =
            steady_clock::now() + seconds(control_sec);
        std_msgs::msg::String msg;
        Json::Value           value;
        value["component"]["level"] = level;
        value["component"]["name"]  = component_name;
        value["component"]["time"]  = -2;

        std::string json_str = value.toStyledString();
        msg.data             = std::move(json_str);

        while (!isHaltRequested() && end > steady_clock::now()) {
            component_ctl->publish(msg);
            std::this_thread::sleep_for(milliseconds(1000));
            LOG_EVERY_N(WARNING, 5) << "pub" << component_name << "control msg";
        }

        return NodeStatus::SUCCESS;
    }

    static PortsList providedPorts() {
        return {InputPort<std::string>("component_name"),
                InputPort<int>("control_gap_sec"),
                InputPort<int>("control_sec"), InputPort<double>("level")};
    }

   private:
    ZbusPublisher::SharedPtr &component_ctl;
};

class Is_component_error : public ConditionNode {
   public:
    Is_component_error(const std::string &name, const NodeConfiguration &config,
                       robot_state &arg)
        : ConditionNode(name, config), robot(arg) {}
    NodeStatus tick() override {
        std::string component_name =
            getInput<std::string>("component_name").value();
        auto component = robot.component_state_map.find(component_name);
        if (component != robot.component_state_map.end()) {
            if (component_name == "fan" && component->second != 0) {
                LOG(INFO) << "海帕检测堵塞异常,error code: "
                          << component->second;
                return NodeStatus::SUCCESS;
            }
            if (component_name == "water_level_sensor" &&
                component->second != 0) {
                LOG(INFO) << "水位传感器异常, error code: "
                          << component->second;
                return NodeStatus::SUCCESS;
            }
            if (component_name == "sewage_water_groove" &&
                component->second != 0) {
                LOG(INFO) << "水槽在位异常, error code: " << component->second;
                return NodeStatus::SUCCESS;
            }
        }
        return NodeStatus::FAILURE;
    }
    static PortsList providedPorts() {
        return {InputPort<std::string>("component_name")};
    }

   private:
    robot_state &robot;
};

class Dust_update_pub : public SyncActionNode {
   public:
    Dust_update_pub(const std::string &name, const NodeConfiguration &config,
                    ZbusPublisher::SharedPtr &arg, robot_state &rbt)
        : SyncActionNode(name, config), dust_update(arg), robot(rbt) {}
    NodeStatus tick() override {
        sensor_msgs::msg::Range dust_msg;
        dust_msg.header.frame_id = "dust_state";
        dust_msg.min_range       = 0;
        dust_msg.max_range       = 1;
        dust_msg.range           = getInput<std::float_t>("value").value();
        dust_update->publish(dust_msg);
        robot.dust_volume = dust_msg.range;
        return NodeStatus::SUCCESS;
    }
    static PortsList providedPorts() {
        return {InputPort<std::float_t>("value")};
    }

   private:
    ZbusPublisher::SharedPtr &dust_update;
    robot_state &             robot;
};

class Workstation_task_pub : public SyncActionNode {
   public:
    Workstation_task_pub(const std::string &       name,
                         const NodeConfiguration & config,
                         ZbusPublisher::SharedPtr &arg)
        : SyncActionNode(name, config), Workstation_task(arg) {}

    NodeStatus tick() override {
        std_msgs::msg::String msg;
        msg.data = getInput<std::string>("pub_name").value();

        LOG(INFO) << "Workstation_task_pub:" << msg.data;
        Workstation_task->publish(msg);
        return NodeStatus::SUCCESS;
    }

    static PortsList providedPorts() {
        return {InputPort<std::string>("pub_name")};
    }

   private:
    ZbusPublisher::SharedPtr &Workstation_task;
};
class Is_clean_water_calibrate : public ConditionNode {
   public:
    Is_clean_water_calibrate(const std::string &      name,
                             const NodeConfiguration &config, robot_state &arg)
        : ConditionNode(name, config), robot(arg) {}

    NodeStatus tick() override {
        if (robot.clean_water_volume <= 0.1) {
            robot.calibrate_add_water_time = true;
            LOG(INFO) << "开始校准加满清水时间";
            return NodeStatus::SUCCESS;
        } else {
            robot.calibrate_add_water_time = false;
            return NodeStatus::FAILURE;
        }
    }

   private:
    robot_state &robot;
};

using namespace BT;

template <typename T>
class IsExpectedNode : public ConditionNode {
   public:
    IsExpectedNode(const std::string &name, const NodeConfiguration &config)
        : ConditionNode(name, config) {}
    NodeStatus tick() override {
        auto res_var      = getInput<T>("variable");
        auto res_expected = getInput<T>("expected");
        if (!res_var)
            throw RuntimeError("Missing required parameter 'variable': " +
                               res_var.error());
        if (!res_expected)
            throw RuntimeError("Missing required parameter 'expected': " +
                               res_expected.error());
        return (res_var.value() == res_expected.value()) ? NodeStatus::SUCCESS
                                                         : NodeStatus::FAILURE;
    }
    static PortsList providedPorts() {
        return {InputPort<T>("variable"), InputPort<T>("expected")};
    }
};

class Is_clean_water_less : public ConditionNode {
   public:
    Is_clean_water_less(const std::string &      name,
                        const NodeConfiguration &config, robot_state &arg)
        : ConditionNode(name, config), robot(arg) {}

    NodeStatus tick() override {
        if (robot.clean_water_volume <=
            getInput<std::float_t>("value").value()) {
            LOG(INFO) << "clean_water_less "
                      << getInput<std::float_t>("value").value() << " SUCCESS";
            return NodeStatus::SUCCESS;
        } else {
            return NodeStatus::FAILURE;
        }
    }

    static PortsList providedPorts() {
        return {InputPort<std::float_t>("value")};
    }

   private:
    robot_state &robot;
};

class Is_clean_water_more : public ConditionNode {
   public:
    Is_clean_water_more(const std::string &      name,
                        const NodeConfiguration &config, robot_state &arg)
        : ConditionNode(name, config), robot(arg) {}

    NodeStatus tick() override {
        if (robot.clean_water_volume >=
            getInput<std::float_t>("value").value()) {
            LOG(INFO) << "clean_water_more "
                      << getInput<std::float_t>("value").value() << " SUCCESS";
            if (robot.clean_water_volume >= 1.0 &&
                robot.calibrate_add_water_time == true) {
                robot.calibrate_add_water_time = false;
            }
            return NodeStatus::SUCCESS;
        } else {
            return NodeStatus::FAILURE;
        }
    }

    static PortsList providedPorts() {
        return {InputPort<std::float_t>("value")};
    }

   private:
    robot_state &robot;
};

class Is_error_happen : public ConditionNode {
   public:
    Is_error_happen(const std::string &name, const NodeConfiguration &config,
                    robot_state &                          arg1,
                    std::vector<workstation_behavior_map> &arg2)
        : ConditionNode(name, config),
          robot(arg1),
          workstation_behavior(arg2) {}

    NodeStatus tick() override {
        if (robot.is_emerg) {
            LOG(INFO) << "机器人急停，退出该流程";
            return NodeStatus::SUCCESS;
        }

        if (!robot.is_in_pile) {
            LOG(INFO) << "机器人不在桩，退出该流程";
            return NodeStatus::SUCCESS;
        }

        if (robot.is_manual_ctrl_abort) {
            LOG(INFO) << "机器人结束手动控制，退出该流程";
            return NodeStatus::SUCCESS;
        }

        if (duration_cast<seconds>(steady_clock::now() -
                                   robot.pile_heartbeat_ts)
                .count() >= 5) {
            LOG(INFO) << "机器人与工作站失去连接，退出该流程";
            return NodeStatus::SUCCESS;
        }

        for (size_t i = 0; i < workstation_behavior.size(); i++) {
            if (workstation_behavior[i].name ==
                getInput<std::string>("behavior").value()) {
                if (workstation_behavior[i].error_ts >=
                    workstation_behavior[i].start_ts) {
                    LOG(INFO) << getInput<std::string>("behavior").value()
                              << "出现异常";
                    return NodeStatus::SUCCESS;
                }
            }
        }

        return NodeStatus::FAILURE;
    }
    static PortsList providedPorts() {
        return {InputPort<std::string>("behavior")};
    }

   private:
    robot_state &                          robot;
    std::vector<workstation_behavior_map> &workstation_behavior;
};

class Is_workstation_start : public ConditionNode {
   public:
    Is_workstation_start(const std::string &      name,
                         const NodeConfiguration &config, robot_state &arg)
        : ConditionNode(name, config), robot(arg) {}

    NodeStatus tick() override {
        if (robot.is_workstation_start != false) {
            LOG(INFO) << "工作站行为树执行--->" << robot.sewage_water_volume;
            return NodeStatus::SUCCESS;
        } else {
            return NodeStatus::FAILURE;
        }
    }

   private:
    robot_state &robot;
};

class Workstation_start_finish : public SyncActionNode {
   public:
    Workstation_start_finish(const std::string &      name,
                             const NodeConfiguration &config, robot_state &arg)
        : SyncActionNode(name, config), robot(arg) {}

    NodeStatus tick() override {
        robot.is_workstation_start = false;
        LOG(INFO) << "工作站行为树执行完成";
        return NodeStatus::SUCCESS;
    }

   private:
    robot_state &robot;
};

class Is_sewage_tank_full : public ConditionNode {
   public:
    Is_sewage_tank_full(const std::string &      name,
                        const NodeConfiguration &config, robot_state &arg)
        : ConditionNode(name, config), robot(arg) {}
    NodeStatus tick() override {
        if (robot.is_sewage_tank_full) {
            LOG(INFO) << "Sewage Tank is Full";
            return NodeStatus::SUCCESS;
        } else {
            return NodeStatus::FAILURE;
        }
    }

   private:
    robot_state &robot;
};

class Is_sewage_pump_error : public ConditionNode {
   public:
    Is_sewage_pump_error(const std::string &      name,
                         const NodeConfiguration &config, robot_state &arg)
        : ConditionNode(name, config), robot(arg) {}
    NodeStatus tick() override {
        if (robot.is_sewage_pump_error) {
            LOG(ERROR) << "污水泵故障";
            return NodeStatus::SUCCESS;
        } else {
            return NodeStatus::FAILURE;
        }
    }

   private:
    robot_state &robot;
};

class Is_need_start : public ConditionNode {
   public:
    Is_need_start(const std::string &name, const NodeConfiguration &config,
                  std::vector<workstation_behavior_map> &arg)
        : ConditionNode(name, config), workstation_behavior(arg) {}

    NodeStatus tick() override {
        for (size_t i = 0; i < workstation_behavior.size(); i++) {
            if (workstation_behavior[i].name ==
                getInput<std::string>("behavior").value()) {
                if (workstation_behavior[i].is_need_start) {
                    LOG(INFO) << workstation_behavior[i].name << "开始或恢复";
                    workstation_behavior[i].is_need_start = false;
                    return NodeStatus::SUCCESS;
                } else {
                    return NodeStatus::FAILURE;
                }

                break;
            }
        }

        LOG(ERROR) << getInput<std::string>("behavior").value()
                   << " no found, please check code";
        return NodeStatus::FAILURE;
    }

    static PortsList providedPorts() {
        return {InputPort<std::string>("behavior")};
    }

   private:
    std::vector<workstation_behavior_map> &workstation_behavior;
};

class Is_sewage_water_less : public ConditionNode {
   public:
    Is_sewage_water_less(const std::string &      name,
                         const NodeConfiguration &config, robot_state &arg)
        : ConditionNode(name, config), robot(arg) {}

    NodeStatus tick() override {
        if (robot.sewage_water_volume <=
            getInput<std::float_t>("value").value()) {
            LOG(INFO) << "sewage_water_less "
                      << getInput<std::float_t>("value").value() << " SUCCESS";
            return NodeStatus::SUCCESS;
        } else {
            return NodeStatus::FAILURE;
        }
    }

    static PortsList providedPorts() {
        return {InputPort<std::float_t>("value")};
    }

   private:
    robot_state &robot;
};

class Is_sewage_water_more : public ConditionNode {
   public:
    Is_sewage_water_more(const std::string &      name,
                         const NodeConfiguration &config, robot_state &arg)
        : ConditionNode(name, config), robot(arg) {}

    NodeStatus tick() override {
        if (robot.sewage_water_volume >=
            getInput<std::float_t>("value").value()) {
            LOG(INFO) << "sewage_water_more "
                      << getInput<std::float_t>("value").value() << " SUCCESS";
            return NodeStatus::SUCCESS;
        } else {
            return NodeStatus::FAILURE;
        }
    }

    static PortsList providedPorts() {
        return {InputPort<std::float_t>("value")};
    }

   private:
    robot_state &robot;
};

class IsAddCleanFluid : public ConditionNode {
   public:
    IsAddCleanFluid(const std::string &name, const NodeConfiguration &config)
        : ConditionNode(name, config) {}

    NodeStatus tick() override {
        bool  is_enable    = false;
        float liquid_ratio = 0.0;

        auto zbus_config_client = ZbusConfigClient();
        int res = zbus_config_client.getConfig("workstation.clean_fluid_enable",
                                               is_enable);
        if (res == ZbusConfigSuccess) {
            LOG(INFO) << "获取清洁液使能配置成功：" << is_enable;
        } else {
            LOG(ERROR) << "获取清洁液使能配置失败";
            return NodeStatus::FAILURE;
        }

        if (!is_enable) {
            LOG(INFO) << "配置：添加清洁液功能关闭";
            return NodeStatus::FAILURE;
        } else {
            LOG(INFO) << "配置：添加清洁液功能开启";
        }

        res = zbus_config_client.getConfig("workstation.clean_fluid_ratio",
                                           liquid_ratio);
        if (res == ZbusConfigSuccess) {
            LOG(INFO) << "获取清洁液配比配置成功:" << liquid_ratio;
            setOutput("clean_fluid_ratio", liquid_ratio);
            return NodeStatus::SUCCESS;
        } else {
            LOG(ERROR) << "获取清洁液配比配置失败";
            return NodeStatus::FAILURE;
        }
        return NodeStatus::FAILURE;
    }
    static PortsList providedPorts() {
        return {OutputPort<float>("clean_fluid_ratio")};
    }
};

class IsDrying : public ConditionNode {
   public:
    IsDrying(const std::string &name, const NodeConfiguration &config,
             robot_state &arg1)
        : ConditionNode(name, config), robot(arg1) {}

    NodeStatus tick() override {
        bool is_enable = false;

        auto zbus_config_client = ZbusConfigClient();
        int  res =
            zbus_config_client.getConfig("workstation.dry_enable", is_enable);
        if (res == ZbusConfigSuccess) {
            LOG(INFO) << "获取烘干配置成功: " << is_enable;
        } else {
            LOG(ERROR) << "获取烘干配置失败";
            return NodeStatus::FAILURE;
        }

        if (is_enable) {
            LOG(INFO) << "配置: 开启烘干";
            return NodeStatus::SUCCESS;
        } else {
            LOG(INFO) << "配置: 关闭烘干";
            return NodeStatus::FAILURE;
        }
    }

   private:
    robot_state &robot;
};

class IsDusting : public ConditionNode {
   public:
    IsDusting(const std::string &name, const NodeConfiguration &config,
              robot_state &arg1)
        : ConditionNode(name, config), robot(arg1) {}

    NodeStatus tick() override {
        bool is_enable = false;

        auto zbus_config_client = ZbusConfigClient();
        int  res =
            zbus_config_client.getConfig("workstation.dust_enable", is_enable);
        if (res == ZbusConfigSuccess) {
            LOG(INFO) << "获取集尘配置成功: " << is_enable;
        } else {
            LOG(ERROR) << "获取集尘配置失败";
            return NodeStatus::FAILURE;
        }

        if (is_enable) {
            LOG(INFO) << "配置: 开启集尘";
            return NodeStatus::SUCCESS;
        } else {
            LOG(INFO) << "配置: 关闭集尘";
            return NodeStatus::FAILURE;
        }
    }

   private:
    robot_state &robot;
};

class Is_timeout : public ConditionNode {
   public:
    Is_timeout(const std::string &name, const NodeConfiguration &config)
        : ConditionNode(name, config) {}

    NodeStatus tick() override {
        if (timeout_started) {
            timeout_started = false;
            start_ts        = steady_clock::now();
            LOG(INFO) << "Is_timeout";
            return NodeStatus::SUCCESS;
        }

        auto duration_hour =
            duration_cast<hours>(steady_clock::now() - start_ts).count();
        if (duration_hour < getInput<std::int64_t>("hour").value()) {
            return NodeStatus::FAILURE;
        }

        auto duration_min =
            duration_cast<minutes>(steady_clock::now() - start_ts).count();
        if (duration_min < getInput<std::int64_t>("min").value()) {
            return NodeStatus::FAILURE;
        }

        auto duration_sec =
            duration_cast<seconds>(steady_clock::now() - start_ts).count();
        if (duration_sec < getInput<std::int64_t>("sec").value()) {
            return NodeStatus::FAILURE;
        }

        auto duration_msec =
            duration_cast<milliseconds>(steady_clock::now() - start_ts).count();
        if (duration_msec < getInput<std::int64_t>("msec").value()) {
            return NodeStatus::FAILURE;
        }

        LOG(INFO) << "Is_timeout";
        start_ts = steady_clock::now();
        return NodeStatus::SUCCESS;
    }

    static PortsList providedPorts() {
        return {InputPort<std::int64_t>("msec"), InputPort<std::int64_t>("sec"),
                InputPort<std::int64_t>("min"),
                InputPort<std::int64_t>("hour")};
    }

   private:
    bool                     timeout_started = true;
    steady_clock::time_point start_ts        = steady_clock::now();
};

class Workstation_is_timeout : public ConditionNode {
   public:
    Workstation_is_timeout(const std::string &                    name,
                           const NodeConfiguration &              config,
                           std::vector<workstation_behavior_map> &behavior,
                           robot_state &                          robot_state)
        : ConditionNode(name, config),
          workstation_behavior(behavior),
          robot(robot_state) {}

    NodeStatus tick() override {
        // Retrieve inputs once.
        std::string behavior_name = getInput<std::string>("behavior").value();
        std::string timeout_str   = getInput<std::string>("timeout").value();

        // Convert timeout from string to integer safely.
        int timeout = 0;
        try {
            timeout = std::stoi(timeout_str);
        } catch (const std::exception &e) {
            LOG(ERROR) << "Invalid timeout value: " << timeout_str
                       << ", error: " << e.what();
            return NodeStatus::FAILURE;
        }

        // Search for the matching behavior.
        for (auto &wb : workstation_behavior) {
            if (wb.name == behavior_name) {
                wb.runtime = static_cast<int>(
                    duration_cast<seconds>(steady_clock::now() - wb.start_ts)
                        .count());

                if (wb.lastruntime != wb.runtime) {
                    wb.lastruntime = wb.runtime;
                    LOG(INFO) << behavior_name << " run " << wb.runtime
                              << ", timeout: " << timeout;
                }

                if (wb.runtime >= timeout) {
                    if (wb.name == "add_clean_water") {
                        robot.is_add_water_timeout = true;
                    }
                    return NodeStatus::SUCCESS;
                } else {
                    if (wb.name == "add_clean_water") {
                        if (robot.calibrate_add_water_time) {
                            robot.add_full_water_time = wb.runtime;
                        }
                        robot.delta_add_water_time = wb.runtime;
                        robot.is_add_water_timeout = false;
                    }
                    return NodeStatus::FAILURE;
                }
            }
        }
        LOG(ERROR) << "Behavior [" << behavior_name
                   << "] not found, please check code";
        return NodeStatus::FAILURE;
    }

    static PortsList providedPorts() {
        return {InputPort<std::string>("behavior"),
                InputPort<std::string>("timeout")};
    }

   private:
    std::vector<workstation_behavior_map> &workstation_behavior;
    robot_state &                          robot;
};

class Is_behavior_timeout : public ConditionNode {
   public:
    Is_behavior_timeout(const std::string &      name,
                        const NodeConfiguration &config, robot_state &arg)
        : ConditionNode(name, config), robot(arg) {}
    NodeStatus tick() override {
        std::string behavior = getInput<std::string>("behavior").value();
        if (behavior == "add_water") {
            if (robot.is_add_water_timeout) {
                LOG(INFO) << "加水超时";
                robot.is_add_water_timeout = false;  // 清空加水超时标志位
                return NodeStatus::SUCCESS;
            }
        }
        return NodeStatus::FAILURE;
    }
    static PortsList providedPorts() {
        return {InputPort<std::string>("behavior")};
    }

   private:
    robot_state &robot;
};

MyDelayNode::MyDelayNode(const std::string &          name,
                         const BT::NodeConfiguration &config)
    : DecoratorNode(name, config) {}

BT::NodeStatus MyDelayNode::tick() {
    if (child_status_.valid()) {
        auto future_status =
            child_status_.wait_for(std::chrono::milliseconds(0));
        if (future_status == std::future_status::ready) {
            auto status = child_status_.get();
            if (status == BT::NodeStatus::RUNNING ||
                status == BT::NodeStatus::IDLE) {
                LOG(ERROR) << "Unexpected child status: " << status;
                return BT::NodeStatus::FAILURE;
            }
            return status;
        } else if (future_status == std::future_status::timeout) {
            return BT::NodeStatus::RUNNING;
        } else {
            LOG(ERROR) << "future_status error!!";
            return BT::NodeStatus::FAILURE;
        }
    }

    {
        std::lock_guard<std::mutex> lk(mutex_);
        is_halt_ = false;
    }
    auto delay_res = getInput<std::uint64_t>("delay_msec");
    if (!delay_res) {
        LOG(ERROR) << "Missing input 'delay_msec': " << delay_res.error();
        return BT::NodeStatus::FAILURE;
    }
    auto delay_msec = *delay_res;

    child_status_ = std::async(
        std::launch::async, &MyDelayNode::async_tick_thread, this, delay_msec);
    return BT::NodeStatus::RUNNING;
}

BT::NodeStatus MyDelayNode::async_tick_thread(uint64_t delay_msec) {
    try {
        if (wait_halt_request(delay_msec)) {
            return BT::NodeStatus::IDLE;
        }

        while (true) {
            auto status = child()->executeTick();
            if (status != BT::NodeStatus::RUNNING) {
                return status;
            }
            if (wait_halt_request(10)) {
                break;
            }
        }
        DecoratorNode::halt();
        return BT::NodeStatus::IDLE;
    } catch (const std::exception &e) {
        std::cerr << "Async thread error: " << e.what() << std::endl;
        return BT::NodeStatus::FAILURE;
    } catch (...) {
        std::cerr << "Unknown async thread error" << std::endl;
        return BT::NodeStatus::FAILURE;
    }
}

bool MyDelayNode::wait_halt_request(uint64_t timeout_msec) {
    std::unique_lock<std::mutex> lock(mutex_);
    return cv_.wait_for(lock, std::chrono::milliseconds(timeout_msec),
                        [this] { return is_halt_; });
}

void MyDelayNode::halt() {
    if (child_status_.valid()) {
        {
            std::lock_guard<std::mutex> lk(mutex_);
            is_halt_ = true;
        }
        cv_.notify_one();
        try {
            child_status_.get();  // 可能抛出异步线程中的异常
        } catch (const std::exception &e) {
            std::cerr << "Halt error: " << e.what() << std::endl;
        } catch (...) {
            std::cerr << "Unknown error in halt()" << std::endl;
        }
    }
}

KeepRunningNode::KeepRunningNode(const std::string &          name,
                                 const BT::NodeConfiguration &config)
    : BT::DecoratorNode(name, config) {}

BT::NodeStatus KeepRunningNode::tick() {
    setStatus(BT::NodeStatus::RUNNING);

    auto child_state = child_node_->executeTick();

    switch (child_state) {
        case BT::NodeStatus::FAILURE: {
            bool condition = getInput<bool>("is_failure_return").value();
            if (condition) {
                return BT::NodeStatus::FAILURE;
            }
            return BT::NodeStatus::RUNNING;
        }
        case BT::NodeStatus::SUCCESS: {
            bool condition = getInput<bool>("is_success_return").value();
            if (condition) {
                return BT::NodeStatus::SUCCESS;
            }
            return BT::NodeStatus::RUNNING;
        }
        case BT::NodeStatus::RUNNING: {
            return BT::NodeStatus::RUNNING;
        }

        default: {
            // TODO throw?
        }
    }
    return status();
}

class Is_need_add_clean_fluid : public ConditionNode {
   public:
    Is_need_add_clean_fluid(const std::string &      name,
                            const NodeConfiguration &config, robot_state &arg)
        : ConditionNode(name, config), robot(arg) {}
    NodeStatus tick() override {
        if (robot.is_need_add_clean_fluid) {
            return NodeStatus::SUCCESS;
        } else {
            return NodeStatus::FAILURE;
        }
    }

   private:
    robot_state &robot;
};

class Process_init : public SyncActionNode {
   public:
    Process_init(const std::string &name, const NodeConfiguration &config,
                 std::vector<workstation_behavior_map> &arg)
        : SyncActionNode(name, config), workstation_behavior(arg) {}

    NodeStatus tick() override {
        for (size_t i = 0; i < workstation_behavior.size(); i++) {
            if (workstation_behavior[i].name ==
                getInput<std::string>("behavior").value()) {
                workstation_behavior[i].start_ts = steady_clock::now();
                LOG(INFO) << "behavior exist: " << workstation_behavior[i].name;
                return NodeStatus::SUCCESS;
            }
        }

        workstation_behavior_map behavior;
        behavior.name     = getInput<std::string>("behavior").value();
        behavior.start_ts = steady_clock::now();
        workstation_behavior.push_back(behavior);
        LOG(INFO) << "register new behavior: " << behavior.name;

        return NodeStatus::SUCCESS;
    }

    static PortsList providedPorts() {
        return {InputPort<std::string>("behavior")};
    }

   private:
    std::vector<workstation_behavior_map> &workstation_behavior;
};

void Workstation_Bt::bt_tick_loop() {
    BehaviorTreeFactory factory;
    Tree                tree;
    PublicParameters    params;
    std::string         model;

    params.getParameter<std::string>("device_model", model, "C3");
    LOG(INFO) << "device_model: " << model;

    NodeBuilder builder_service_call = [=](const std::string &      name,
                                           const NodeConfiguration &config) {
        return std::make_unique<Service_call>(name, config, this->service_call,
                                              this->workstation_zbus_node_ptr_);
    };
    factory.registerBuilder<Service_call>("Service_call", builder_service_call);

    NodeBuilder builder_SetBlackboard2 = [=](const std::string &      name,
                                             const NodeConfiguration &config) {
        return std::make_unique<SetBlackboardN<2>>(name, config);
    };
    factory.registerBuilder<SetBlackboardN<2>>("SetBlackboard2",
                                               builder_SetBlackboard2);

    NodeBuilder builder_SetBlackboard3 = [=](const std::string &      name,
                                             const NodeConfiguration &config) {
        return std::make_unique<SetBlackboardN<3>>(name, config);
    };
    factory.registerBuilder<SetBlackboardN<3>>("SetBlackboard3",
                                               builder_SetBlackboard3);

    NodeBuilder builder_SetBlackboard4 = [=](const std::string &      name,
                                             const NodeConfiguration &config) {
        return std::make_unique<SetBlackboardN<4>>(name, config);
    };
    factory.registerBuilder<SetBlackboardN<4>>("SetBlackboard4",
                                               builder_SetBlackboard4);

    NodeBuilder builder_start_charge = [=](const std::string &      name,
                                           const NodeConfiguration &config) {
        return std::make_unique<Start_charge>(name, config,
                                              this->auto_charge_start_pub_ptr_);
    };
    factory.registerBuilder<Start_charge>("Start_charge", builder_start_charge);

    NodeBuilder builder_get_process_type =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<Get_process_type>(name, config,
                                                      this->robot);
        };
    factory.registerBuilder<Get_process_type>("Get_process_type",
                                              builder_get_process_type);

    NodeBuilder builder_process_type_finish =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<Process_type_finish>(name, config,
                                                         this->robot);
        };
    factory.registerBuilder<Process_type_finish>("Process_type_finish",
                                                 builder_process_type_finish);

    NodeBuilder builder_update_robot_state =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<Update_Robot_State>(
                name, config, this->water_position_update_pub_ptr_,
                this->charge_feedback_update_pub_ptr_,
                this->sewage_tank_update_pub_ptr_,
                this->robot_dust_position_update_ptr);
        };
    factory.registerBuilder<Update_Robot_State>("Update_Robot_State",
                                                builder_update_robot_state);

    NodeBuilder builder_get_manual_ctrl_process =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<Get_manual_ctrl_process>(name, config,
                                                             this->robot);
        };
    factory.registerBuilder<Get_manual_ctrl_process>(
        "Get_manual_ctrl_process", builder_get_manual_ctrl_process);

    NodeBuilder builder_manual_ctrl_start_finish =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<manual_ctrl_finish>(name, config,
                                                        this->robot);
        };
    factory.registerBuilder<manual_ctrl_finish>(
        "manual_ctrl_finish", builder_manual_ctrl_start_finish);

    NodeBuilder builder_calc_add_clean_fluid_time =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<CalcAddCleanFluidTime>(name, config,
                                                           this->robot);
        };
    factory.registerBuilder<CalcAddCleanFluidTime>(
        "CalcAddCleanFluidTime", builder_calc_add_clean_fluid_time);

    NodeBuilder builder_force_set_clean_fluid =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<Force_set_clean_fluid>(name, config,
                                                           this->robot);
        };
    factory.registerBuilder<Force_set_clean_fluid>(
        "Force_set_clean_fluid", builder_force_set_clean_fluid);

    factory.registerNodeType<Process_return>("Process_return");

    NodeBuilder builder_dust_update_pub = [=](const std::string &      name,
                                              const NodeConfiguration &config) {
        return std::make_unique<Dust_update_pub>(
            name, config, this->dust_position_update_pub_ptr_, this->robot);
    };
    factory.registerBuilder<Dust_update_pub>("Dust_update_pub",
                                             builder_dust_update_pub);

    NodeBuilder builder_waiting_charge_state =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<Waiting_charge_state>(name, config,
                                                          this->robot);
        };
    factory.registerBuilder<Waiting_charge_state>("Waiting_charge_state",
                                                  builder_waiting_charge_state);

    factory.registerNodeType<Workstation_blackboard>("Workstation_blackboard");

    NodeBuilder builder_pub_state_log = [=](const std::string &      name,
                                            const NodeConfiguration &config) {
        return std::make_unique<PubStateLog>(
            name, config, this->run_log_pub_ptr_, pipeline_log_pub_ptr_,
            float_log_pub_ptr_);
    };
    factory.registerBuilder<PubStateLog>("PubStateLog", builder_pub_state_log);

    NodeBuilder builder_run_log_pub = [=](const std::string &      name,
                                          const NodeConfiguration &config) {
        return std::make_unique<Run_log_pub>(
            name, config, this->run_log_pub_ptr_, pipeline_log_pub_ptr_,
            float_log_pub_ptr_);
    };
    factory.registerBuilder<Run_log_pub>("Run_log_pub", builder_run_log_pub);

    NodeBuilder builder_component_ctrl = [=](const std::string &      name,
                                             const NodeConfiguration &config) {
        return std::make_unique<Single_Component_Ctrl>(
            name, config, this->component_ctrl_pub_ptr_);
    };
    factory.registerBuilder<Single_Component_Ctrl>("Single_Component_Ctrl",
                                                   builder_component_ctrl);

    NodeBuilder builder_is_component_error =
        [=](const std::string name, const NodeConfiguration &config) {
            return std::make_unique<Is_component_error>(name, config,
                                                        this->robot);
        };
    factory.registerBuilder<Is_component_error>("Is_component_error",
                                                builder_is_component_error);

    NodeBuilder builder_workstation_task_pub =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<Workstation_task_pub>(
                name, config, this->workstation_task_pub_ptr_);
        };
    factory.registerBuilder<Workstation_task_pub>("Workstation_task_pub",
                                                  builder_workstation_task_pub);

    NodeBuilder builder_is_clean_water_less =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<Is_clean_water_less>(name, config,
                                                         this->robot);
        };
    factory.registerBuilder<Is_clean_water_less>("Is_clean_water_less",
                                                 builder_is_clean_water_less);
    NodeBuilder builder_is_clean_water_calibrate =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<Is_clean_water_calibrate>(name, config,
                                                              this->robot);
        };
    factory.registerBuilder<Is_clean_water_calibrate>(
        "Is_clean_water_calibrate", builder_is_clean_water_calibrate);
    NodeBuilder builder_is_clean_water_more =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<Is_clean_water_more>(name, config,
                                                         this->robot);
        };
    factory.registerBuilder<Is_clean_water_more>("Is_clean_water_more",
                                                 builder_is_clean_water_more);

    NodeBuilder builder_error_happen = [=](const std::string &      name,
                                           const NodeConfiguration &config) {
        return std::make_unique<Is_error_happen>(name, config, this->robot,
                                                 this->workstation_behavior);
    };
    factory.registerBuilder<Is_error_happen>("Is_error_happen",
                                             builder_error_happen);

    NodeBuilder builder_is_sewage_tank_full =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<Is_sewage_tank_full>(name, config,
                                                         this->robot);
        };
    factory.registerBuilder<Is_sewage_tank_full>("Is_sewage_tank_full",
                                                 builder_is_sewage_tank_full);

    NodeBuilder builder_is_sewage_pump_error =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<Is_sewage_pump_error>(name, config,
                                                          this->robot);
        };
    factory.registerBuilder<Is_sewage_pump_error>("Is_sewage_pump_error",
                                                  builder_is_sewage_pump_error);

    NodeBuilder builder_is_workstation_start =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<Is_workstation_start>(name, config,
                                                          this->robot);
        };
    factory.registerBuilder<Is_workstation_start>("Is_workstation_start",
                                                  builder_is_workstation_start);

    NodeBuilder builder_workstation_start_finish =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<Workstation_start_finish>(name, config,
                                                              this->robot);
        };
    factory.registerBuilder<Workstation_start_finish>(
        "Workstation_start_finish", builder_workstation_start_finish);

    NodeBuilder builder_is_need_start = [=](const std::string &      name,
                                            const NodeConfiguration &config) {
        return std::make_unique<Is_need_start>(name, config,
                                               this->workstation_behavior);
    };
    factory.registerBuilder<Is_need_start>("Is_need_start",
                                           builder_is_need_start);

    NodeBuilder builder_is_sewage_water_less =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<Is_sewage_water_less>(name, config,
                                                          this->robot);
        };

    factory.registerBuilder<Is_sewage_water_less>("Is_sewage_water_less",
                                                  builder_is_sewage_water_less);
    NodeBuilder builder_is_sewage_water_more =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<Is_sewage_water_more>(name, config,
                                                          this->robot);
        };

    factory.registerBuilder<Is_sewage_water_more>("Is_sewage_water_more",
                                                  builder_is_sewage_water_more);

    NodeBuilder builder_isAddCleanFluid = [=](const std::string &      name,
                                              const NodeConfiguration &config) {
        return std::make_unique<IsAddCleanFluid>(name, config);
    };
    factory.registerBuilder<IsAddCleanFluid>("IsAddCleanFluid",
                                             builder_isAddCleanFluid);

    NodeBuilder builder_IsDrying = [=](const std::string &      name,
                                       const NodeConfiguration &config) {
        return std::make_unique<IsDrying>(name, config, this->robot);
    };
    factory.registerBuilder<IsDrying>("IsDrying", builder_IsDrying);

    NodeBuilder builder_IsDusting = [=](const std::string &      name,
                                        const NodeConfiguration &config) {
        return std::make_unique<IsDusting>(name, config, this->robot);
    };
    factory.registerBuilder<IsDusting>("IsDusting", builder_IsDusting);

    factory.registerNodeType<Is_timeout>("Is_timeout");

    NodeBuilder builder_workstation_is_timeout =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<Workstation_is_timeout>(
                name, config, this->workstation_behavior, this->robot);
        };
    factory.registerBuilder<Workstation_is_timeout>(
        "Workstation_is_timeout", builder_workstation_is_timeout);

    NodeBuilder builder_is_behavior_timeout =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<Is_behavior_timeout>(name, config,
                                                         this->robot);
        };
    factory.registerBuilder<Is_behavior_timeout>("Is_behavior_timeout",
                                                 builder_is_behavior_timeout);

    NodeBuilder builder_is_need_add_clean_fluid =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<Is_need_add_clean_fluid>(name, config,
                                                             this->robot);
        };
    factory.registerBuilder<Is_need_add_clean_fluid>(
        "Is_need_add_clean_fluid", builder_is_need_add_clean_fluid);

    NodeBuilder builder_get_clean_water_position =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<Get_clean_water_position>(name, config,
                                                              this->robot);
        };
    factory.registerBuilder<Get_clean_water_position>(
        "Get_clean_water_position", builder_get_clean_water_position);

    NodeBuilder builder_get_last_task_type =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<GetLastTaskType>(name, config, this->robot);
        };
    factory.registerBuilder<GetLastTaskType>("GetLastTaskType",
                                             builder_get_last_task_type);

    NodeBuilder builder_is_clean_water_volume_change =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<Is_clean_water_volume_change>(name, config,
                                                                  this->robot);
        };
    factory.registerBuilder<Is_clean_water_volume_change>(
        "Is_clean_water_volume_change", builder_is_clean_water_volume_change);

    NodeBuilder builder_process_init = [=](const std::string &      name,
                                           const NodeConfiguration &config) {
        return std::make_unique<Process_init>(name, config,
                                              this->workstation_behavior);
    };
    factory.registerBuilder<Process_init>("Process_init", builder_process_init);

    NodeBuilder builder_my_delay_node = [=](const std::string &      name,
                                            const NodeConfiguration &config) {
        return std::make_unique<MyDelayNode>(name, config);
    };
    factory.registerBuilder<MyDelayNode>("MyDelayNode", builder_my_delay_node);

    NodeBuilder builder_keep_running_node =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<KeepRunningNode>(name, config);
        };
    factory.registerBuilder<KeepRunningNode>("KeepRunning",
                                             builder_keep_running_node);

    NodeBuilder builder_voiceswitch_blackboard =
        [=](const std::string &name, const NodeConfiguration &config) {
            return std::make_unique<VoiceSwitchBlackboard>(name, config);
        };
    factory.registerBuilder<VoiceSwitchBlackboard>(
        "VoiceSwitchBlackboard", builder_voiceswitch_blackboard);

    if (model == "C3") {
        tree = factory.createTreeFromFile(
            "./install/workstation_bt/workstation_c3.xml");
    } else if (model == "C5") {
        tree = factory.createTreeFromFile(
            "./install/workstation_bt/workstation_c5.xml");
    } else {
        LOG(FATAL) << "device_model error, get: " << model;
    }
    // This logger prints state changes on console
    StdCoutLogger logger_cout(tree);

    // This logger publish status changes using ZeroMQ. Used by Groot
    // PublisherZMQ publisher_zmq(tree, 25, 1888, 1889);

    printTreeRecursively(tree.rootNode());

    while (1) {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        tree.tickRoot();
    }
}

void Workstation_Bt::workstationStartCallback(
    const std_srvs::srv::SetBool::Request::SharedPtr  recv_data,
    const std_srvs::srv::SetBool::Response::SharedPtr ack_data) {
    (void) recv_data;
    if (robot.is_workstation_start) {
        ack_data->success = false;
        LOG(WARNING) << "workstation start running, repeat request";
    } else {
        robot.is_workstation_start = true;
        ack_data->success          = true;
        LOG(INFO) << "get workstation start request";
    }
}

void Workstation_Bt::batteryCallback(
    const sensor_msgs::msg::BatteryState::SharedPtr battery) {
    if (robot.battery_percent != battery->percentage) {
        LOG(INFO) << "电池电量由：" << robot.battery_percent << "，变为："
                  << battery->percentage;
    }
    if (robot.power_supply_status != (int) battery->power_supply_status) {
        LOG(INFO) << "电池充电状态由：" << robot.power_supply_status
                  << "，变为：" << (int) battery->power_supply_status;
    }
    robot.battery_percent     = battery->percentage;
    robot.power_supply_status = (int) battery->power_supply_status;
}

void Workstation_Bt::waterPositionCallback(
    const sensor_msgs::msg::Range::SharedPtr water_position) {
    if (water_position->header.frame_id == "clean_water") {
        if (robot.clean_water_volume != water_position->range) {
            LOG(INFO) << "清水量由：" << robot.clean_water_volume << "，变为："
                      << water_position->range;
        }
        robot.clean_water_volume = water_position->range;
    } else if (water_position->header.frame_id == "waste_water") {
        if (robot.sewage_water_volume != water_position->range) {
            LOG(INFO) << "污水量为：" << robot.sewage_water_volume << "，变为："
                      << water_position->range;
        }
        robot.sewage_water_volume = water_position->range;
    }
}

void Workstation_Bt::dustPositionCallback(
    const sensor_msgs::msg::Range::SharedPtr dust_position) {
    if (dust_position->header.frame_id == "dust_state") {
        if (robot.dust_volume != dust_position->range) {
            LOG(INFO) << "尘盒量由：" << robot.dust_volume << "，变为："
                      << dust_position->range;
        }
        robot.dust_volume = dust_position->range;
    }
}

void Workstation_Bt::sewage_tank_stateCallback(
    const std_msgs::msg::Bool::SharedPtr tank_state) {
    if (robot.is_sewage_tank_full != tank_state->data) {
        int current_tank_state = static_cast<int>(tank_state->data);
        LOG(INFO) << "污水槽量由：" << robot.is_sewage_tank_full << "，转变为："
                  << current_tank_state;
    }
    robot.is_sewage_tank_full = tank_state->data;
}

void Workstation_Bt::sewage_pump_error_stateCallback(
    const std_msgs::msg::Bool::SharedPtr error_state) {
    if (robot.is_sewage_pump_error != error_state->data) {
        LOG(INFO) << "污水泵错误状态由: " << robot.is_sewage_pump_error
                  << "，转变为: " << error_state->data;
    }
    robot.is_sewage_pump_error = error_state->data;
}

void Workstation_Bt::processTypeCallback(
    const std_msgs::msg::String::SharedPtr type) {
    Json::Value  type_supply;
    Json::Reader reader;

    bool success = reader.parse(type->data, type_supply);

    if (!success) {
        LOG(ERROR) << "Failed to parse JSON: "
                   << reader.getFormattedErrorMessages()
                   << ",data: " << type->data;
        return;
    }

    robot.water_supply_status = type_supply["water_supply"].asBool();
    LOG(INFO) << "water_supply_status: " << robot.water_supply_status
              << ",workstation_start: " << robot.is_workstation_start;
}

void Workstation_Bt::autoChargeStateCallback(
    const std_msgs::msg::String::SharedPtr state) {
    robot.charge_state = state->data;
    LOG(INFO) << "自动充电状态为：" << state->data;
}

void Workstation_Bt::emergCallback(const std_msgs::msg::Bool::SharedPtr state) {
    robot.is_emerg = state->data;
    LOG(INFO) << "机器人急停状态：" << state->data;
}

void Workstation_Bt::chargeFeedbackCallback(
    const std_msgs::msg::UInt8::SharedPtr state) {
    if (state->data == 1) {
        robot.is_in_pile     = true;
        robot.is_auto_charge = true;
    } else if (state->data == 2) {
        robot.is_in_pile     = true;
        robot.is_auto_charge = false;
    } else {
        robot.is_in_pile = false;
    }

    LOG(INFO) << "压桩信号反馈：" << (int) state->data;
}

void Workstation_Bt::pileHeartCallback(
    const std_msgs::msg::UInt8::SharedPtr state) {
    (void) state;
    robot.pile_heartbeat_ts = steady_clock::now();
}

void Workstation_Bt::workstationStateCallback(
    const chassis_interfaces::msg::WorkstationLog::SharedPtr
        workstation_state) {
    chassis_interfaces::msg::UploadRunLog log_msg;
    log_msg.log_type         = workstation_state->log_type;
    log_msg.application_name = workstation_state->application_name;
    log_msg.log_value        = workstation_state->log_value;
    run_log_pub_ptr_->publish(log_msg);
}

void Workstation_Bt::componentStateCallback(
    const std_msgs::msg::String::SharedPtr state) {
    Json::Value  root;
    Json::Reader reader;

    std::string component_name;
    int         component_error_code;
    bool        success = reader.parse(state->data, root);
    if (!success) {
        LOG(ERROR) << "Failed to parse JSON"
                   << reader.getFormattedErrorMessages();
        return;
    }

    component_name       = root["dev_name"].asString();
    component_error_code = root["error_code"].asInt();

    robot.component_state_map[component_name] = component_error_code;

    LOG(INFO) << "dev_name:" << component_name
              << " ,error_code:" << component_error_code;
}

void Workstation_Bt::workstationErrorCallback(
    const std_msgs::msg::String::SharedPtr behavior) {
    for (size_t i = 0; i < workstation_behavior.size(); i++) {
        if (workstation_behavior[i].name == behavior->data) {
            LOG(INFO) << "sub workstation error: " << behavior->data;
            workstation_behavior[i].error_ts = steady_clock::now();
            return;
        }
    }

    LOG(INFO) << "no matched exception: " << behavior->data;
}

Workstation_Bt::Workstation_Bt() {
    workstation_zbus_node_ptr_ = std::make_shared<ZmqNode>();

    run_log_pub_ptr_ =
        workstation_zbus_node_ptr_->create_publisher("/state_pub");

    pipeline_log_pub_ptr_ =
        workstation_zbus_node_ptr_->create_publisher("/pipeline_log");

    float_log_pub_ptr_ =
        workstation_zbus_node_ptr_->create_publisher("/exception_report");

    auto_charge_start_pub_ptr_ =
        workstation_zbus_node_ptr_->create_publisher("/auto_charge/pause");

    water_position_update_pub_ptr_ =
        workstation_zbus_node_ptr_->create_publisher("/water_position/update");

    sewage_tank_update_pub_ptr_ = workstation_zbus_node_ptr_->create_publisher(
        "/workstation/sewage_tank_check/update");

    charge_feedback_update_pub_ptr_ =
        workstation_zbus_node_ptr_->create_publisher(
            "/auto_charge/charge_feedback/update");

    workstation_task_pub_ptr_ =
        workstation_zbus_node_ptr_->create_publisher("/robot_event");

    component_ctrl_pub_ptr_ =
        workstation_zbus_node_ptr_->create_publisher("/mcu/component_control");

    dust_position_update_pub_ptr_ =
        workstation_zbus_node_ptr_->create_publisher(
            "/workstation/dust_position");

    robot_dust_position_update_ptr =
        workstation_zbus_node_ptr_->create_publisher(
            "/robot/dust_position/update");

    workstation_start_ptr_ =
        workstation_zbus_node_ptr_->create_service<std_srvs::srv::SetBool>(
            "/workstation/start",
            std::bind(&Workstation_Bt::workstationStartCallback, this,
                      std::placeholders::_1, std::placeholders::_2));
    workstation_manual_ctrl_ptr_ = workstation_zbus_node_ptr_->create_service(
        "/workstation/manual_ctrl",
        std::bind(&Workstation_Bt::workstationManualCtrlCallback, this,
                  std::placeholders::_1, std::placeholders::_2));

    battery_sub_ptr_ =
        workstation_zbus_node_ptr_
            ->create_subscription<sensor_msgs::msg::BatteryState>(
                "/battery", std::bind(&Workstation_Bt::batteryCallback, this,
                                      std::placeholders::_1));

    water_position_sub_ptr_ =
        workstation_zbus_node_ptr_
            ->create_subscription<sensor_msgs::msg::Range>(
                "/water_position",
                std::bind(&Workstation_Bt::waterPositionCallback, this,
                          std::placeholders::_1));

    sewage_tank_sub_ptr_ =
        workstation_zbus_node_ptr_->create_subscription<std_msgs::msg::Bool>(
            "/workstation/sewage_tank_check",
            std::bind(&Workstation_Bt::sewage_tank_stateCallback, this,
                      std::placeholders::_1));

    sewage_pump_error_sub_ptr_ =
        workstation_zbus_node_ptr_->create_subscription<std_msgs::msg::Bool>(
            "/workstation/sewage_pump_error_state",
            std::bind(&Workstation_Bt::sewage_pump_error_stateCallback, this,
                      std::placeholders::_1));

    dust_position_update_sub_ptr_ =
        workstation_zbus_node_ptr_
            ->create_subscription<sensor_msgs::msg::Range>(
                "/robot/dust_position",
                std::bind(&Workstation_Bt::dustPositionCallback, this,
                          std::placeholders::_1));

    process_type_sub_ptr_ =
        workstation_zbus_node_ptr_->create_subscription<std_msgs::msg::String>(
            "/workstation/process_type",
            std::bind(&Workstation_Bt::processTypeCallback, this,
                      std::placeholders::_1));

    auto_charge_state_sub_ptr_ =
        workstation_zbus_node_ptr_->create_subscription<std_msgs::msg::String>(
            "/auto_charge/state",
            std::bind(&Workstation_Bt::autoChargeStateCallback, this,
                      std::placeholders::_1));

    emerg_sub_ptr_ =
        workstation_zbus_node_ptr_->create_subscription<std_msgs::msg::Bool>(
            "/emerg", std::bind(&Workstation_Bt::emergCallback, this,
                                std::placeholders::_1));

    charge_feedback_sub_ptr_ =
        workstation_zbus_node_ptr_->create_subscription<std_msgs::msg::UInt8>(
            "/auto_charge/charge_feedback",
            std::bind(&Workstation_Bt::chargeFeedbackCallback, this,
                      std::placeholders::_1));

    pile_heartbeat_sub_ptr_ =
        workstation_zbus_node_ptr_->create_subscription<std_msgs::msg::UInt8>(
            "/auto_charge/pile_heartbeat",
            std::bind(&Workstation_Bt::pileHeartCallback, this,
                      std::placeholders::_1));

    workstation_state_sub_ptr_ =
        workstation_zbus_node_ptr_
            ->create_subscription<chassis_interfaces::msg::WorkstationLog>(
                "/workstation_state_pub",
                std::bind(&Workstation_Bt::workstationStateCallback, this,
                          std::placeholders::_1));
    component_state_sub_ptr_ =
        workstation_zbus_node_ptr_->create_subscription<std_msgs::msg::String>(
            "/component_state/pub",
            std::bind(&Workstation_Bt::componentStateCallback, this,
                      std::placeholders::_1));

    workstation_error_sub_ptr_ =
        workstation_zbus_node_ptr_->create_subscription<std_msgs::msg::String>(
            "/workstation/error",
            std::bind(&Workstation_Bt::workstationErrorCallback, this,
                      std::placeholders::_1));

    pile_get_dev_info_client_ptr_ = workstation_zbus_node_ptr_->create_client(
        "/auto_charge/pile_get_dev_info");

    http_request_wrapper_client_ptr_ =
        workstation_zbus_node_ptr_->create_client(
            "/mqtt_bridge/http_wrapper_request");

    get_dev_info_config_ptr_ =
        workstation_zbus_node_ptr_->create_client("/device_info/config/get");

    LOG(INFO) << "Constructor end";

    bt_thread = std::thread(&Workstation_Bt::bt_tick_loop, this);
    // 启动后同步机器状态
    syncRobotState();
}

void Workstation_Bt::syncRobotState() {
    std_msgs::msg::Empty empty;
    // 水量状态同步
    water_position_update_pub_ptr_->publish(empty);
    // 在桩状态同步
    charge_feedback_update_pub_ptr_->publish(empty);
    // 工作站污水槽状态同步
    sewage_tank_update_pub_ptr_->publish(empty);
    // 尘盒量状态同步
    robot_dust_position_update_ptr->publish(empty);

    LOG(INFO) << "Initial robot state synchronized.";
}

Workstation_Bt::~Workstation_Bt() {
    LOG(INFO) << "Workstation_Bt Destructor";
    LOG(INFO) << "Destructor end";
}