/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> cheng<PERSON>@cvte.com
 * @Date: 2022-06-28 22:06:48
 * @LastEditors: cheng<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-07-11 09:47:40
 * @FilePath: /navi_manager_bt/Inc/Bt/loggers_rm_limit/bt_cout_logger.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置
 * 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef BT_COUT_LOGGER_H
#define BT_COUT_LOGGER_H

#include <cstring>
#include <glog/logging.h>
#include "behaviortree_cpp_v3/loggers/abstract_logger.h"

/**
 * @description: 去除原本库对于创建数量的限制,修改为glog输出
 * @return {*}
 */
namespace BT {
/**
 * @brief AddStdCoutLoggerToTree. Give  the root node of a tree,
 * a simple callback is subscribed to any status change of each node.
 *
 *
 * @param root_node
 * @return Important: the returned shared_ptr must not go out of scope,
 *         otherwise the logger is removed.
 */

class StdCoutLogger : public StatusChangeLogger {
   public:
    StdCoutLogger(const BT::Tree &tree);
    ~StdCoutLogger() override;

    virtual void callback(Duration timestamp, const TreeNode &node,
                          NodeStatus prev_status, NodeStatus status) override;

    virtual void flush() override;
};

}  // namespace BT

#endif  // BT_COUT_LOGGER_H
