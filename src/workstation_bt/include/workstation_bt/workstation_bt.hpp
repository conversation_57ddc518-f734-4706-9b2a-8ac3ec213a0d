#include "behaviortree_cpp_v3/bt_factory.h"
#include "bt_cout_logger.h"
#include "behaviortree_cpp_v3/loggers/bt_minitrace_logger.h"
#include "behaviortree_cpp_v3/loggers/bt_file_logger.h"
#include "behaviortree_cpp_v3/loggers/bt_zmq_publisher.h"
#include "behaviortree_cpp_v3/decorator_node.h"
#include "behaviortree_cpp_v3/decorators/timer_queue.h"

#include "std_msgs/msg/empty.hpp"
#include "std_msgs/msg/bool.hpp"
#include "std_msgs/msg/u_int8.hpp"
#include "std_msgs/msg/string.hpp"
#include "std_srvs/srv/set_bool.hpp"
#include "sensor_msgs/msg/battery_state.hpp"
#include "sensor_msgs/msg/range.hpp"
#include "chassis_interfaces/msg/upload_run_log.hpp"
#include "chassis_interfaces/msg/workstation_log.hpp"
#include "chassis_interfaces/srv/pile_get_dev_info.hpp"
#include "chassis_interfaces/srv/http_request_wrapper.hpp"
#include "chassis_interfaces/srv/get_string_map.hpp"

#include "zbus_interface.hpp"
#include "zmq/zmq_node.hpp"
#include "zbus_config_client.hpp"

#include <glog/logging.h>
#include <jsoncpp/json/json.h>
#include <vector>
#include <unordered_map>
#include <cpptime.h>
#include <atomic>
#include <future>
#include <public_parameters/PublicParameters.hpp>

#define DEFAULT_ADD_WATER_TIME 300  // 默认配置从空到满加清水的时间

using namespace std::chrono;
using namespace BT;

enum ManualRespondCode {
    SUCCESS            = 0,
    EMERGENCY          = 1,
    NOT_IN_PILE        = 2,
    STATE_UNCHANGED    = 3,
    PARAM_ERROR        = 4,
    OTHER_CTRL_RUNNING = 5,
    SUPPLYING          = 6,
    WATER_FULL         = 7,
    WATER_EMPTY        = 8,
};

class service_call_map {
   public:
    std::string           name;
    ZbusClient::SharedPtr ptr;
};

class workstation_behavior_map {
   public:
    std::string              name;
    bool                     is_need_start;
    steady_clock::time_point start_ts;
    steady_clock::time_point pause_ts;
    steady_clock::time_point error_ts;
    int                      runtime;
    int                      lastruntime;
};

class MyDelayNode : public BT::DecoratorNode {
   public:
    MyDelayNode(const std::string &name, const BT::NodeConfiguration &config);

    virtual ~MyDelayNode() override { halt(); }

    virtual void halt() override;

    static BT::PortsList providedPorts() {
        return {BT::InputPort<uint64_t>(
            "delay_msec", "Tick the child after a few milliseconds")};
    }

   private:
    virtual BT::NodeStatus      tick() override;
    BT::NodeStatus              async_tick_thread(uint64_t delay_msec);
    bool                        wait_halt_request(uint64_t timeout_msec);
    std::future<BT::NodeStatus> child_status_;

    std::mutex              mutex_;
    std::condition_variable cv_;
    bool                    is_halt_ = false;
};

class KeepRunningNode : public BT::DecoratorNode {
   public:
    KeepRunningNode(const std::string &          name,
                    const BT::NodeConfiguration &config);

    virtual ~KeepRunningNode() override = default;

    static BT::PortsList providedPorts() {
        return {BT::InputPort<bool>("is_failure_return"),
                BT::InputPort<bool>("is_success_return")};
    }

   private:
    virtual BT::NodeStatus tick() override;
};

class robot_state {
   public:
    bool is_in_pile              = false;
    bool is_auto_charge          = false;
    bool is_emerg                = false;
    bool is_workstation_start    = false;
    bool is_manual_ctrl_start    = false;
    bool is_manual_ctrl_abort    = false;
    bool is_add_water_timeout    = false;
    bool is_need_add_clean_fluid = false;
    bool is_sewage_tank_full     = false;
    bool is_sewage_pump_error    = false;

    bool water_supply_status      = false;
    bool calibrate_add_water_time = false;

    steady_clock::time_point pile_heartbeat_ts = steady_clock::now();

    float clean_water_volume       = 0.5;
    float last_clean_water_volume  = 0.0;
    float delta_clean_water_volume = 0.0;
    float clean_fluid_ratio        = 0.0;
    float sewage_water_volume      = 0.0;
    float dust_volume              = 0.0;
    float battery_percent          = 0.5;
    int   power_supply_status      = 3;
    int   add_full_water_time      = DEFAULT_ADD_WATER_TIME;  // s
    int   delta_add_water_time     = 0;                       // s

    std::unordered_map<std::string, int>              component_state_map;
    std::map<std::string, std::optional<std::string>> config_map_;
    // 存储所有手动控制状态
    std::unordered_map<std::string, bool> manual_ctrl_list;

    std::string charge_state = "Idle";
    std::string process_type = "water_cycle";
};

class Workstation_Bt {
   public:
    Workstation_Bt();
    ~Workstation_Bt();

   private:
    void workstationStartCallback(
        const std_srvs::srv::SetBool::Request::SharedPtr  recv_data,
        const std_srvs::srv::SetBool::Response::SharedPtr ack_data);
    void workstationManualCtrlCallback(const nlohmann::json &request,
                                       nlohmann::json &      respond);
    bool checkMember(const nlohmann::json &root, const std::string &key,
                     std::string &reason, nlohmann::json::value_t type);
    void batteryCallback(
        const sensor_msgs::msg::BatteryState::SharedPtr battery);
    void waterPositionCallback(
        const sensor_msgs::msg::Range::SharedPtr water_position);
    void dustPositionCallback(
        const sensor_msgs::msg::Range::SharedPtr dust_position);
    void processTypeCallback(const std_msgs::msg::String::SharedPtr type);
    void autoChargeStateCallback(const std_msgs::msg::String::SharedPtr state);
    void emergCallback(const std_msgs::msg::Bool::SharedPtr state);
    void chargeFeedbackCallback(const std_msgs::msg::UInt8::SharedPtr state);
    void pileHeartCallback(const std_msgs::msg::UInt8::SharedPtr state);
    void sewage_tank_stateCallback(
        const std_msgs::msg::Bool::SharedPtr tank_state);
    void sewage_pump_error_stateCallback(
        const std_msgs::msg::Bool::SharedPtr error_state);
    void workstationStateCallback(
        const chassis_interfaces::msg::WorkstationLog::SharedPtr
            workstation_state);
    void componentStateCallback(const std_msgs::msg::String::SharedPtr state);
    void workstationErrorCallback(
        const std_msgs::msg::String::SharedPtr behavior);
    void bt_tick_loop();
    void syncRobotState();

   public:
    ZbusNode::SharedPtr workstation_zbus_node_ptr_;

    std::vector<service_call_map>         service_call;
    std::vector<workstation_behavior_map> workstation_behavior;
    robot_state                           robot;

   private:
    std::thread                 bt_thread;
    ZbusPublisher::SharedPtr    run_log_pub_ptr_;
    ZbusPublisher::SharedPtr    pipeline_log_pub_ptr_;
    ZbusPublisher::SharedPtr    float_log_pub_ptr_;
    ZbusPublisher::SharedPtr    auto_charge_start_pub_ptr_;
    ZbusPublisher::SharedPtr    water_position_update_pub_ptr_;
    ZbusPublisher::SharedPtr    sewage_tank_update_pub_ptr_;
    ZbusPublisher::SharedPtr    charge_feedback_update_pub_ptr_;
    ZbusService::SharedPtr      workstation_start_ptr_;
    ZbusService::SharedPtr      workstation_manual_ctrl_ptr_;
    ZbusPublisher::SharedPtr    workstation_task_pub_ptr_;
    ZbusPublisher::SharedPtr    dust_position_update_pub_ptr_;
    ZbusPublisher::SharedPtr    component_ctrl_pub_ptr_;
    ZbusPublisher::SharedPtr    robot_dust_position_update_ptr;
    ZbusSubscription::SharedPtr battery_sub_ptr_;
    ZbusSubscription::SharedPtr water_position_sub_ptr_;
    ZbusSubscription::SharedPtr process_type_sub_ptr_;
    ZbusSubscription::SharedPtr auto_charge_state_sub_ptr_;
    ZbusSubscription::SharedPtr emerg_sub_ptr_;
    ZbusSubscription::SharedPtr sewage_tank_sub_ptr_;
    ZbusSubscription::SharedPtr sewage_pump_error_sub_ptr_;
    ZbusSubscription::SharedPtr charge_feedback_sub_ptr_;
    ZbusSubscription::SharedPtr pile_heartbeat_sub_ptr_;
    ZbusSubscription::SharedPtr component_state_sub_ptr_;
    ZbusSubscription::SharedPtr workstation_state_sub_ptr_;
    ZbusSubscription::SharedPtr workstation_error_sub_ptr_;
    ZbusSubscription::SharedPtr dust_position_update_sub_ptr_;
    ZbusClient::SharedPtr       pile_get_dev_info_client_ptr_;
    ZbusClient::SharedPtr       http_request_wrapper_client_ptr_;
    ZbusClient::SharedPtr       get_dev_info_config_ptr_;
};