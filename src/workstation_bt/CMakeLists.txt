cmake_minimum_required(VERSION 3.5)
project(workstation_bt)

# Default to C++14
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 17)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic -g)
endif()

set(CMAKE_BUILD_TYPE "DEBUG")
set(CMAKE_CXX_FLAGS_DEBUG "$ENV{CXXFLAGS} -O0 -Wall -g2 -ggdb")
set(CMAKE_CXX_FLAGS_RELEASE "ENV{CXXFLAGS} -O3 -Wall")

include(../../3rdparty/3rdparty.cmake)

find_package(ament_cmake REQUIRED)
find_package(std_srvs REQUIRED)
find_package(std_msgs REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(chassis_interfaces REQUIRED)
find_package(zbus_cpp REQUIRED)
find_package(cvte_log REQUIRED)
find_package(public_parameters REQUIRED)

add_executable(${PROJECT_NAME} 
  src/main.cpp
  src/workstation_bt.cpp
  src/loggers_limit/bt_cout_logger.cpp
)

target_include_directories(${PROJECT_NAME}
  PRIVATE
    include
    include/workstation_bt
    include/loggers_limit
    include/cpptime
  )

ament_target_dependencies(${PROJECT_NAME} 
  std_srvs 
  std_msgs 
  sensor_msgs 
  chassis_interfaces
  zbus_cpp
  public_parameters
  cvte_log
)

target_link_libraries(${PROJECT_NAME} 
  -lglog
  behavior_tree
  jsoncpp
)

install(TARGETS
  ${PROJECT_NAME}
  DESTINATION lib/${PROJECT_NAME})

install(FILES ../../workstation_c3.xml ../../workstation_c5.xml
  DESTINATION ./)

ament_package()