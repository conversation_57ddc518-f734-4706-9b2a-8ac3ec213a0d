find_package(Boost COMPONENTS coroutine QUIET)
if(Boost_FOUND)
    string(REPLACE "." "0" Boost_VERSION_NODOT ${Boost_VERSION})
    if(NOT Boost_VERSION_NODOT VERSION_LESS 105900)
        message(STATUS "Found boost::coroutine2.")
        add_definitions(-DBT_BOOST_COROUTINE2)
        set(BT_COROUTINES true)
    elseif(NOT Boost_VERSION_NODOT VERSION_LESS 105300)
        message(STATUS "Found boost::coroutine.")
        add_definitions(-DBT_BOOST_COROUTINE)
        set(BT_COROUTINES true)
    endif()
    include_directories(${Boost_INCLUDE_DIRS})
endif()

include_directories(
    ../../3rdparty/BehaviorTree.CPP/include
    ../../3rdparty/BehaviorTree.CPP/3rdparty
)

list(APPEND BT_SOURCE
    ../../3rdparty/BehaviorTree.CPP/src/action_node.cpp
    ../../3rdparty/BehaviorTree.CPP/src/basic_types.cpp
    ../../3rdparty/BehaviorTree.CPP/src/behavior_tree.cpp
    ../../3rdparty/BehaviorTree.CPP/src/blackboard.cpp
    ../../3rdparty/BehaviorTree.CPP/src/bt_factory.cpp
    ../../3rdparty/BehaviorTree.CPP/src/decorator_node.cpp
    ../../3rdparty/BehaviorTree.CPP/src/condition_node.cpp
    ../../3rdparty/BehaviorTree.CPP/src/control_node.cpp
    ../../3rdparty/BehaviorTree.CPP/src/shared_library.cpp
    ../../3rdparty/BehaviorTree.CPP/src/tree_node.cpp
    ../../3rdparty/BehaviorTree.CPP/src/xml_parsing.cpp

    ../../3rdparty/BehaviorTree.CPP/src/decorators/inverter_node.cpp
    ../../3rdparty/BehaviorTree.CPP/src/decorators/repeat_node.cpp
    ../../3rdparty/BehaviorTree.CPP/src/decorators/retry_node.cpp
    ../../3rdparty/BehaviorTree.CPP/src/decorators/subtree_node.cpp
    ../../3rdparty/BehaviorTree.CPP/src/decorators/delay_node.cpp

    ../../3rdparty/BehaviorTree.CPP/src/controls/if_then_else_node.cpp
    ../../3rdparty/BehaviorTree.CPP/src/controls/fallback_node.cpp
    ../../3rdparty/BehaviorTree.CPP/src/controls/parallel_node.cpp
    ../../3rdparty/BehaviorTree.CPP/src/controls/reactive_sequence.cpp
    ../../3rdparty/BehaviorTree.CPP/src/controls/reactive_fallback.cpp
    ../../3rdparty/BehaviorTree.CPP/src/controls/sequence_node.cpp
    ../../3rdparty/BehaviorTree.CPP/src/controls/sequence_star_node.cpp
    ../../3rdparty/BehaviorTree.CPP/src/controls/switch_node.cpp
    ../../3rdparty/BehaviorTree.CPP/src/controls/while_do_else_node.cpp

    ../../3rdparty/BehaviorTree.CPP/src/loggers/bt_cout_logger.cpp
    ../../3rdparty/BehaviorTree.CPP/src/loggers/bt_file_logger.cpp
    ../../3rdparty/BehaviorTree.CPP/src/loggers/bt_minitrace_logger.cpp

    ../../3rdparty/BehaviorTree.CPP/3rdparty/tinyxml2/tinyxml2.cpp
    ../../3rdparty/BehaviorTree.CPP/3rdparty/minitrace/minitrace.cpp
)

if (UNIX)
    list(APPEND BT_SOURCE ../../3rdparty/BehaviorTree.CPP/src/shared_library_UNIX.cpp )
endif()

add_library(behavior_tree SHARED ${BT_SOURCE})

target_link_libraries(behavior_tree
    ${Boost_LIBRARIES}
)

install(TARGETS behavior_tree
  DESTINATION lib
)