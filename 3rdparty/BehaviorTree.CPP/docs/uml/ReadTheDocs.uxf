<?xml version="1.0" encoding="UTF-8"?><diagram program="umlet" version="13.3">
  <zoom_level>10</zoom_level>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>290</x>
      <y>100</y>
      <w>100</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Sequence
</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>350</x>
      <y>160</y>
      <w>160</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>Build Awesome
Robot Behaviors</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>330</x>
      <y>120</y>
      <w>100</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>80.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>180</x>
      <y>160</y>
      <w>150</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>RetryUntilSuccessful</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>330</x>
      <y>70</y>
      <w>30</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;30.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLUseCase</id>
    <coordinates>
      <x>130</x>
      <y>280</y>
      <w>110</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>isItClear?</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>240</x>
      <y>120</y>
      <w>120</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;40.0;100.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>240</x>
      <y>190</y>
      <w>30</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;30.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>280</x>
      <y>50</y>
      <w>120</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>*BehaviorTree*
bg=gray

</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>200</x>
      <y>220</y>
      <w>100</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Fallback
</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>260</x>
      <y>280</y>
      <w>120</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>Read
Documentation</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>170</x>
      <y>240</y>
      <w>100</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>80.0;10.0;15.0;40.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>240</x>
      <y>240</y>
      <w>100</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;10.0;80.0;40.0</additional_attributes>
  </element>
</diagram>
