<?xml version="1.0" encoding="UTF-8"?><diagram program="umlet" version="13.3">
  <zoom_level>10</zoom_level>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>480</x>
      <y>150</y>
      <w>90</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Shoot</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>350</x>
      <y>70</y>
      <w>100</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Sequence
fg=blue</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>390</x>
      <y>90</y>
      <w>150</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>130.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLUseCase</id>
    <coordinates>
      <x>190</x>
      <y>150</y>
      <w>130</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>isEnemyVisible?</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLUseCase</id>
    <coordinates>
      <x>330</x>
      <y>150</y>
      <w>130</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>isRifleLoaded?</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>240</x>
      <y>90</y>
      <w>180</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;60.0;160.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>390</x>
      <y>90</y>
      <w>30</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>760</x>
      <y>70</y>
      <w>150</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>ReactiveSequence
fg=blue</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLUseCase</id>
    <coordinates>
      <x>690</x>
      <y>140</y>
      <w>130</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>isEnemyVisible?</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>750</x>
      <y>90</y>
      <w>100</w>
      <h>70</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;50.0;80.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>850</x>
      <y>150</y>
      <w>150</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>ApproachEnemy
</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>820</x>
      <y>90</y>
      <w>120</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>100.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>300</x>
      <y>340</y>
      <w>170</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>ReactiveSequence
</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLUseCase</id>
    <coordinates>
      <x>250</x>
      <y>400</y>
      <w>120</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>isBatteryOK?</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>300</x>
      <y>360</y>
      <w>100</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;40.0;80.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>370</x>
      <y>360</y>
      <w>100</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>80.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>410</x>
      <y>400</y>
      <w>140</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>SequenceStar
fg=blue
</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>360</x>
      <y>420</y>
      <w>140</w>
      <h>70</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;50.0;120.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>470</x>
      <y>420</y>
      <w>30</w>
      <h>70</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>470</x>
      <y>420</y>
      <w>140</w>
      <h>70</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>120.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>540</x>
      <y>470</y>
      <w>100</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>GoTo
(goal=C")</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>430</x>
      <y>470</y>
      <w>100</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>GoTo
(goal=B")</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>320</x>
      <y>470</y>
      <w>100</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>GoTo
(goal=A")</panel_attributes>
    <additional_attributes/>
  </element>
</diagram>
