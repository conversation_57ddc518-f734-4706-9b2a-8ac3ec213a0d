<svg xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg" xmlns:xhtml="http://www.w3.org/1999/xhtml" style="background-color:#fff" id="svg66" width="651" height="161" version="1.1" viewBox="-0.5 -0.5 651 161"><metadata id="metadata72"/><g id="g58"><path id="path2" stroke-miterlimit="10" d="m 350,50 -65.17,55.86" pointer-events="stroke" style="fill:none;stroke:#000;stroke-miterlimit:10"/><path id="path4" stroke-miterlimit="10" d="m 280.85,109.27 3.04,-7.21 0.94,3.8 3.61,1.51 z" pointer-events="all" style="fill:#000;stroke:#000;stroke-miterlimit:10"/><path id="path6" stroke-miterlimit="10" d="m 350,50 60.32,55.68" pointer-events="stroke" style="fill:none;stroke:#000;stroke-miterlimit:10"/><path id="path8" stroke-miterlimit="10" d="m 414.18,109.24 -7.52,-2.17 3.66,-1.39 1.09,-3.76 z" pointer-events="all" style="fill:#000;stroke:#000;stroke-miterlimit:10"/><path id="path10" stroke-miterlimit="10" d="m 350,50 193.9,58.17" pointer-events="stroke" style="fill:none;stroke:#000;stroke-miterlimit:10"/><path id="path12" stroke-miterlimit="10" d="m 548.93,109.68 -7.71,1.34 2.68,-2.85 -0.67,-3.86 z" pointer-events="all" style="fill:#000;stroke:#000;stroke-miterlimit:10"/><path id="path14" stroke-miterlimit="10" d="M 350,50 91.21,108.59" pointer-events="stroke" style="fill:none;stroke:#000;stroke-miterlimit:10"/><path id="path16" stroke-miterlimit="10" d="m 86.09,109.75 6.05,-4.96 -0.93,3.8 2.48,3.03 z" pointer-events="all" style="fill:#000;stroke:#000;stroke-miterlimit:10"/><rect id="rect18" width="120" height="40" x="290" y="10" pointer-events="all" style="fill:#fff;stroke:#000"/><g id="g24" transform="translate(-0.5,-0.5)"><switch id="switch22"><foreignObject style="overflow:visible;text-align:left" width="100%" height="100%" pointer-events="none" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><xhtml:div style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:118px;height:1px;padding-top:30px;margin-left:291px"><xhtml:div style="box-sizing:border-box;font-size:0;text-align:center" data-drawio-colors="color: rgb(0, 0, 0);"><xhtml:div style="display:inline-block;font-size:18px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:all;white-space:normal;overflow-wrap:normal">Sequence</xhtml:div></xhtml:div></xhtml:div></foreignObject><text id="text20" x="350" y="35" font-size="18" style="font-size:18px;font-family:Helvetica;text-anchor:middle;fill:#000">Sequence</text></switch></g><rect id="rect26" width="120" height="40" x="190" y="110" pointer-events="all" style="fill:#fff;stroke:#000"/><g id="g32" transform="translate(-0.5,-0.5)"><switch id="switch30"><foreignObject style="overflow:visible;text-align:left" width="100%" height="100%" pointer-events="none" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><xhtml:div style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:118px;height:1px;padding-top:130px;margin-left:191px"><xhtml:div style="box-sizing:border-box;font-size:0;text-align:center" data-drawio-colors="color: rgb(0, 0, 0);"><xhtml:div style="display:inline-block;font-size:18px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:all;white-space:normal;overflow-wrap:normal">OpenGripper</xhtml:div></xhtml:div></xhtml:div></foreignObject><text id="text28" x="250" y="135" font-size="18" style="font-size:18px;font-family:Helvetica;text-anchor:middle;fill:#000">OpenGripper</text></switch></g><rect id="rect34" width="150" height="40" x="340" y="110" pointer-events="all" style="fill:#fff;stroke:#000"/><g id="g40" transform="translate(-0.5,-0.5)"><switch id="switch38"><foreignObject style="overflow:visible;text-align:left" width="100%" height="100%" pointer-events="none" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><xhtml:div style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:148px;height:1px;padding-top:130px;margin-left:341px"><xhtml:div style="box-sizing:border-box;font-size:0;text-align:center" data-drawio-colors="color: rgb(0, 0, 0);"><xhtml:div style="display:inline-block;font-size:18px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:all;white-space:normal;overflow-wrap:normal">ApproachObject</xhtml:div></xhtml:div></xhtml:div></foreignObject><text id="text36" x="415" y="135" font-size="18" style="font-size:18px;font-family:Helvetica;text-anchor:middle;fill:#000">ApproachObject</text></switch></g><rect id="rect42" width="120" height="40" x="520" y="110" pointer-events="all" style="fill:#fff;stroke:#000"/><g id="g48" transform="translate(-0.5,-0.5)"><switch id="switch46"><foreignObject style="overflow:visible;text-align:left" width="100%" height="100%" pointer-events="none" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><xhtml:div style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:118px;height:1px;padding-top:130px;margin-left:521px"><xhtml:div style="box-sizing:border-box;font-size:0;text-align:center" data-drawio-colors="color: rgb(0, 0, 0);"><xhtml:div style="display:inline-block;font-size:18px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:all;white-space:normal;overflow-wrap:normal">CloseGripper</xhtml:div></xhtml:div></xhtml:div></foreignObject><text id="text44" x="580" y="135" font-size="18" style="font-size:18px;font-family:Helvetica;text-anchor:middle;fill:#000">CloseGripper</text></switch></g><rect id="rect50" width="150" height="40" x="10" y="110" pointer-events="all" rx="14.4" ry="14.4" style="fill:#fff;stroke:#000"/><g id="g56" transform="translate(-0.5,-0.5)"><switch id="switch54"><foreignObject style="overflow:visible;text-align:left" width="100%" height="100%" pointer-events="none" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><xhtml:div style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:148px;height:1px;padding-top:130px;margin-left:11px"><xhtml:div style="box-sizing:border-box;font-size:0;text-align:center" data-drawio-colors="color: rgb(0, 0, 0);"><xhtml:div style="display:inline-block;font-size:18px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:all;white-space:normal;overflow-wrap:normal">CheckBattery</xhtml:div></xhtml:div></xhtml:div></foreignObject><text id="text52" x="85" y="135" font-size="18" style="font-size:18px;font-family:Helvetica;text-anchor:middle;fill:#000">CheckBattery</text></switch></g></g></svg>