name: ros1

on: [push, pull_request]

jobs:
  industrial_ci:
    strategy:
      matrix:
        env:
          - {ROS_DISTRO: melodic, ROS_REPO: main}
          - {ROS_DISTRO: noetic, ROS_REPO: main}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v1
      - uses: 'ros-industrial/industrial_ci@master'
        env: ${{matrix.env}}
        with:
            package-name: behaviortree_cpp_v3
