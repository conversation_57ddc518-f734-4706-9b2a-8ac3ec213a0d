<?xml version="1.0"?>
<root main_tree_to_execute="BehaviorTree">
    <!-- ////////// -->
    <BehaviorTree ID="AddCleanFluidSubtree">
        <ForceSuccess>
            <Sequence name="添加清洁液子树">
                <Sequence name="添加清洁液条件判断">
                    <Inverter>
                        <Sequence>
                            <Condition ID="Is_error_happen" behavior="NULL"/>
                            <Action ID="Workstation_start_finish" name="添加清洁液异常结束"/>
                        </Sequence>
                    </Inverter>
                    <Decorator ID="PubStateLog" application_name="workstation_bt/no_add_fluid" expect="FAILURE" float_log_type="" float_log_value="" log_type="warn" log_value="水位无变化，不添加清洁液" pipeline_log_type="" pipeline_log_value="">
                        <BlackboardCheckString name="判断加水后清水量是否变化" return_on_mismatch="FAILURE" value_A="{water_level_is_change}" value_B="true">
                            <Condition ID="IsAddCleanFluid" clean_fluid_ratio="{ratio}" name="判断是否需要加清洁液"/>
                        </BlackboardCheckString>
                    </Decorator>
                </Sequence>
                <SubTree ID="CalcAddFluidTimeProc" __autoremap="true" __shared_blackboard="false" add_clean_fluid_time="{clean_fluid_time}" clean_fluid_ratio="{ratio}"/>
                <Sequence name="开始添加清洁液">
                    <Action ID="Workstation_blackboard" output_key1="cleanfluid_behavior" output_key2="robot_cleanfluid_service" output_key3="add_cleanfluid_timeout" output_key4="workstation_cleanfluid_service" value1="add_clean_fluid" value2="NULL" value3="{clean_fluid_time}" value4="/workstation/add_clean_fluid"/>
                    <Decorator ID="PubStateLog" application_name="software/add_clean_fluid_error" expect="FAILURE" float_log_type="" float_log_value="" log_type="warn" log_value="[工作站]:调用加清洁液服务失败" pipeline_log_type="" pipeline_log_value="">
                        <SubTree ID="ServiceCallSubtree" __autoremap="true" __shared_blackboard="false" behavior="{cleanfluid_behavior}" robot_service_name="{robot_cleanfluid_service}" status="1" workstation_service_name="{workstation_cleanfluid_service}"/>
                    </Decorator>
                </Sequence>
                <Parallel failure_threshold="1" name="添加清洁液流程" success_threshold="1">
                    <Decorator ID="KeepRunning" is_failure_return="false" is_success_return="true" name="添加清洁液体异常检测">
                        <Condition ID="Is_error_happen" behavior="{cleanfluid_behavior}"/>
                    </Decorator>
                    <Decorator ID="KeepRunning" is_failure_return="false" is_success_return="true" name="等待添加清洁液结束">
                        <Condition ID="Workstation_is_timeout" behavior="{cleanfluid_behavior}" timeout="{add_cleanfluid_timeout}"/>
                    </Decorator>
                </Parallel>
                <SubTree ID="ServiceCallSubtree" __autoremap="true" __shared_blackboard="false" behavior="{cleanfluid_behavior}" robot_service_name="{robot_cleanfluid_service}" status="0" workstation_service_name="{workstation_cleanfluid_service}"/>
            </Sequence>
        </ForceSuccess>
    </BehaviorTree>
    <!-- ////////// -->
    <BehaviorTree ID="AddDrainParallelSubtree">
        <Parallel failure_threshold="1" name="加排水并行" success_threshold="2">
            <Sequence>
                <SubTree ID="AddWaterSubtree" __autoremap="true" __shared_blackboard="false" ctrl_mode="auto" water_level_is_change="{is_change}"/>
                <SubTree ID="AddCleanFluidSubtree" __autoremap="true" __shared_blackboard="false" water_level_is_change="{is_change}"/>
            </Sequence>
            <SubTree ID="DrainWaterSubtree" __autoremap="true" __shared_blackboard="false" ctrl_mode="auto"/>
        </Parallel>
    </BehaviorTree>
    <!-- ////////// -->
    <BehaviorTree ID="AddWaterSubtree">
        <ForceSuccess>
            <Sequence name="加水流程子树">
                <Inverter>
                    <Fallback name="加水条件判断">
                        <Sequence>
                            <Condition ID="Is_error_happen" behavior="NULL" name="机器是否发生异常"/>
                            <Action ID="Workstation_start_finish" name="加水异常结束"/>
                        </Sequence>
                        <Sequence>
                            <Condition ID="Is_clean_water_more" name="清水量是否为满" value="1.0"/>
                            <SetBlackboard name="重置加水变化标志" output_key="water_level_is_change" value="false"/>
                        </Sequence>
                        <Decorator ID="PubStateLog" application_name="workstation_bt/add_water_error" expect="SUCCESS" float_log_type="" float_log_value="" log_type="error" log_value="水位传感器异常，跳过加水阶段" pipeline_log_type="" pipeline_log_value="">
                            <Condition ID="Is_component_error" component_name="water_level_sensor" name="水位传感器是否发生故障"/>
                        </Decorator>
                    </Fallback>
                </Inverter>
                <Action ID="Get_clean_water_position" name="获取加水前清水量液位" water_current_level="{water_level}"/>
                <Action ID="VoiceSwitchBlackboard" auto_pub_end_voice="add_water_end" auto_pub_start_voice="add_water_start" ctrl_mode="{ctrl_mode}" manual_pub_end_voice="manual_add_water_end" manual_pub_start_voice="manual_add_water_start" voice_pub_end="{pub_end}" voice_pub_start="{pub_start}"/>
                <Action ID="Workstation_task_pub" name="语音播报" pub_name="{pub_start}"/>
                <Sequence name="开始加水">
                    <Decorator ID="PubStateLog" application_name="software/workstation" expect="SUCCESS" float_log_type="" float_log_value="" log_type="" log_value="" pipeline_log_type="info" pipeline_log_value="supply/add_clean_water">
                        <Action ID="Workstation_blackboard" name="开始加水" output_key1="addwater_behavior" output_key2="robot_addwater_service" output_key3="addwater_timeout" output_key4="workstation_addwater_service" value1="add_clean_water" value2="/robot/add_clean_water" value3="300" value4="/workstation/add_clean_water"/>
                    </Decorator>
                    <Decorator ID="PubStateLog" application_name="workstation_bt/add_water_error" expect="FAILURE" float_log_type="" float_log_value="" log_type="warn" log_value="[工作站]:调用加水服务失败" pipeline_log_type="" pipeline_log_value="">
                        <SubTree ID="ServiceCallSubtree" __autoremap="true" __shared_blackboard="false" behavior="{addwater_behavior}" robot_service_name="{robot_addwater_service}" status="1" workstation_service_name="{workstation_addwater_service}"/>
                    </Decorator>
                </Sequence>
                <Parallel failure_threshold="1" name="加水" success_threshold="1">
                    <Decorator ID="KeepRunning" is_failure_return="false" is_success_return="true" name="加水直到满">
                        <Condition ID="Is_clean_water_more" value="1.0"/>
                    </Decorator>
                    <Decorator ID="KeepRunning" is_failure_return="false" is_success_return="true" name="加水异常检测">
                        <Fallback>
                            <Condition ID="Is_error_happen" behavior="{addwater_behavior}"/>
                            <Decorator ID="PubStateLog" application_name="workstation_bt/add_water_error" expect="SUCCESS" float_log_type="" float_log_value="" log_type="error" log_value="加水过程中水位传感器异常，终止加水阶段" pipeline_log_type="" pipeline_log_value="">
                                <Condition ID="Is_component_error" component_name="water_level_sensor"/>
                            </Decorator>
                        </Fallback>
                    </Decorator>
                    <Sequence name="加水是否超时">
                        <Decorator ID="KeepRunning" is_failure_return="false" is_success_return="true" name="加水超时检测">
                            <Condition ID="Workstation_is_timeout" behavior="{addwater_behavior}" timeout="{addwater_timeout}"/>
                        </Decorator>
                        <IfThenElse name="加水超时">
                            <Condition ID="Is_clean_water_volume_change" is_change="{water_level_is_change}" last_water_level="{water_level}" name="判断清水量是否有变化"/>
                            <Action ID="Run_log_pub" application_name="workstation_bt/add_water_timeout" float_log_type="true" float_log_value="hardware/add_water_timeout" log_type="warn" log_value="机器人加水超时，清水量液位有变化，若水车有水请检查加清水组件是否异常" pipeline_log_type="error" pipeline_log_value="supply/add_water_timeout"/>
                            <Action ID="Run_log_pub" application_name="workstation_bt/add_water_timeout" float_log_type="true" float_log_value="hardware/add_water_timeout" log_type="error" log_value="机器人加水超时，清水量液位无变化，请检查水车是否有水" pipeline_log_type="error" pipeline_log_value="supply/add_water_timeout"/>
                        </IfThenElse>
                    </Sequence>
                </Parallel>
                <Condition ID="Is_clean_water_volume_change" is_change="{water_level_is_change}" last_water_level="{water_level}" name="判断清水量是否有变化"/>
                <SubTree ID="ServiceCallSubtree" __autoremap="true" __shared_blackboard="false" behavior="{addwater_behavior}" robot_service_name="{robot_addwater_service}" status="0" workstation_service_name="{workstation_addwater_service}"/>
                <Action ID="Workstation_task_pub" name="语音播报" pub_name="{pub_end}"/>
            </Sequence>
        </ForceSuccess>
    </BehaviorTree>
    <!-- ////////// -->
    <BehaviorTree ID="BehaviorTree">
        <Sequence>
            <Action ID="Update_Robot_State" name="更新机器状态"/>
            <KeepRunningUntilFailure>
                <IfThenElse>
                    <Action ID="Get_process_type" process="{process}"/>
                    <Sequence name="补给流程开始">
                        <Action ID="Update_Robot_State" name="更新机器状态"/>
                        <Action ID="Start_charge" data="false" name="暂停充电"/>
                        <Decorator ID="MyDelayNode" delay_msec="2000">
                            <Switch3 case_1="DuringTaskSupply" case_2="WholeProcessSupply" case_3="ManualSupply" name="补给流程选择" variable="{process}">
                                <SubTree ID="DuringTaskSupplySubtree" __autoremap="false" __shared_blackboard="false"/>
                                <SubTree ID="WholeProcessSupplySubtree" __autoremap="false" __shared_blackboard="false"/>
                                <SubTree ID="ManualSupplySubtree" __autoremap="false" __shared_blackboard="false"/>
                                <AlwaysSuccess/>
                            </Switch3>
                        </Decorator>
                    </Sequence>
                    <Action ID="Start_charge" data="true"/>
                </IfThenElse>
            </KeepRunningUntilFailure>
        </Sequence>
    </BehaviorTree>
    <!-- ////////// -->
    <BehaviorTree ID="CalcAddFluidTimeProc">
        <IfThenElse name="添加清洁液时间计算">
            <Condition ID="Is_behavior_timeout" behavior="add_water" name="判断加水是否超时"/>
            <Decorator ID="PubStateLog" application_name="workstation_bt/force_set_add_fluid" expect="SUCCESS" float_log_type="" float_log_value="" log_type="warn" log_value="加水超时但水位有变化，强制设置添加清洁液时间" pipeline_log_type="" pipeline_log_value="">
                <Action ID="Force_set_clean_fluid" clean_fluid="{add_clean_fluid_time}" clean_fluid_ratio="{clean_fluid_ratio}" name="强制设置添加清洁液时间"/>
            </Decorator>
            <Action ID="CalcAddCleanFluidTime" clean_capacity="4000" clean_fluid="{add_clean_fluid_time}" clean_fluid_ratio="{clean_fluid_ratio}" clean_fluid_traffic="350" name="计算添加清洁液时间"/>
        </IfThenElse>
    </BehaviorTree>
    <!-- ////////// -->
    <BehaviorTree ID="DrainWaterSubtree">
        <ForceSuccess>
            <Sequence name="排污子树">
                <Sequence name="排污条件判断">
                    <Condition ID="Is_sewage_water_more" name="是否需要排污" value="0.1"/>
                    <Inverter>
                        <Sequence>
                            <Condition ID="Is_error_happen" behavior="NULL" name="机器是否发生异常"/>
                            <Action ID="Workstation_start_finish" name="排污异常结束"/>
                        </Sequence>
                    </Inverter>
                </Sequence>
                <Sequence name="开始排污">
                    <Decorator ID="PubStateLog" application_name="software/workstation" expect="SUCCESS" float_log_type="" float_log_value="" log_type="" log_value="" pipeline_log_type="info" pipeline_log_value="supply/sewage_water">
                        <Action ID="Workstation_blackboard" name="开始排污" output_key1="sewage_behavior" output_key2="robot_sewage_service" output_key3="sewage_timeout" output_key4="workstation_sewage_service" value1="sewage_water" value2="/robot/sewage_water" value3="150" value4="/workstation/sewage_water"/>
                    </Decorator>
                    <Decorator ID="PubStateLog" application_name="workstation/sewage_water_error" expect="FAILURE" float_log_type="" float_log_value="" log_type="warn" log_value="[工作站]:调用排污服务失败" pipeline_log_type="" pipeline_log_value="">
                        <SubTree ID="ServiceCallSubtree" __autoremap="true" __shared_blackboard="false" behavior="{sewage_behavior}" robot_service_name="{robot_sewage_service}" status="1" workstation_service_name="{workstation_sewage_service}"/>
                    </Decorator>
                </Sequence>
                <Action ID="Workstation_task_pub" pub_name="drain_water_start"/>
                <Parallel failure_threshold="1" name="排污" success_threshold="1">
                    <Decorator ID="KeepRunning" is_failure_return="false" is_success_return="true" name="排水直到空">
                        <Condition ID="Is_sewage_water_less" value="0.0"/>
                    </Decorator>
                    <Decorator ID="KeepRunning" is_failure_return="false" is_success_return="true" name="排污异常检测">
                        <Fallback>
                            <Condition ID="Is_error_happen" behavior="{sewage_behavior}"/>
                            <Condition ID="Is_sewage_pump_error"/>
                            <SubTree ID="SewageTankFullSubtree" __autoremap="true" __shared_blackboard="false" robot_service_name="{robot_sewage_service}"/>
                        </Fallback>
                    </Decorator>
                    <Sequence name="排污是否超时">
                        <Decorator ID="KeepRunning" is_failure_return="false" is_success_return="true" name="排污超时检测">
                            <Condition ID="Workstation_is_timeout" behavior="{sewage_behavior}" timeout="{sewage_timeout}"/>
                        </Decorator>
                        <Action ID="Run_log_pub" application_name="workstation_bt/sewage_water_timeout" float_log_type="true" float_log_value="hardware/drain_water_timeout" log_type="warn" log_value="机器人排污超时" pipeline_log_type="" pipeline_log_value=""/>
                    </Sequence>
                </Parallel>
                <SubTree ID="ServiceCallSubtree" __autoremap="true" __shared_blackboard="false" behavior="{sewage_behavior}" robot_service_name="{robot_sewage_service}" status="0" workstation_service_name="{workstation_sewage_service}"/>
                <SubTree ID="SewageLevelDoubleCheck" __autoremap="false" __shared_blackboard="false"/>
                <Action ID="Workstation_task_pub" pub_name="drain_water_end"/>
            </Sequence>
        </ForceSuccess>
    </BehaviorTree>
    <!-- ////////// -->
    <BehaviorTree ID="DryingSubtree">
        <ForceSuccess>
            <Sequence name="烘干子树">
                <Sequence name="烘干条件判断">
                    <Condition ID="IsDrying" name="判断烘干是否打开"/>
                    <Inverter>
                        <Sequence name="烘干是否发生异常">
                            <Condition ID="Is_error_happen" behavior="NULL" name="机器是否发生异常"/>
                            <Action ID="Workstation_start_finish" name="烘干异常结束"/>
                        </Sequence>
                    </Inverter>
                </Sequence>
                <Action ID="Workstation_task_pub" pub_name="dry_start"/>
                <Sequence name="开始烘干">
                    <Decorator ID="PubStateLog" application_name="software/workstation" expect="SUCCESS" float_log_type="" float_log_value="" log_type="" log_value="" pipeline_log_type="info" pipeline_log_value="supply/start_drying">
                        <Action ID="Workstation_blackboard" name="开始烘干" output_key1="drying_behavior" output_key2="robot_drying_service" output_key3="drying_timeout" output_key4="workstation_drying_service" value1="drying" value2="/robot/drying" value3="7200" value4="/workstation/drying"/>
                    </Decorator>
                    <Decorator ID="PubStateLog" application_name="software/workstation" expect="FAILURE" float_log_type="" float_log_value="" log_type="warn" log_value="[工作站]:调用烘干服务失败" pipeline_log_type="" pipeline_log_value="">
                        <SubTree ID="ServiceCallSubtree" __autoremap="true" __shared_blackboard="false" behavior="{drying_behavior}" robot_service_name="{robot_drying_service}" status="1" workstation_service_name="{workstation_drying_service}"/>
                    </Decorator>
                </Sequence>
                <Parallel failure_threshold="1" name="烘干流程" success_threshold="1">
                    <Decorator ID="KeepRunning" is_failure_return="false" is_success_return="true" name="烘干是否异常">
                        <Condition ID="Is_error_happen" behavior="{drying_behavior}"/>
                    </Decorator>
                    <Decorator ID="KeepRunning" is_failure_return="false" is_success_return="true" name="等待烘干结束">
                        <Condition ID="Workstation_is_timeout" behavior="{drying_behavior}" timeout="{drying_timeout}"/>
                    </Decorator>
                </Parallel>
                <SubTree ID="ServiceCallSubtree" __autoremap="true" __shared_blackboard="false" behavior="{drying_behavior}" robot_service_name="{robot_drying_service}" status="0" workstation_service_name="{workstation_drying_service}"/>
            </Sequence>
        </ForceSuccess>
    </BehaviorTree>
    <!-- ////////// -->
    <BehaviorTree ID="DuringTaskSupplySubtree">
        <Sequence name="任务中补给流程">
            <SubTree ID="HepaDetectSubtree"/>
            <SubTree ID="AddDrainParallelSubtree" __autoremap="false" __shared_blackboard="false"/>
            <Action ID="Process_type_finish" name="标记任务中补给结束" type="water_supply"/>
            <Action ID="Workstation_start_finish" name="任务中补给结束"/>
        </Sequence>
    </BehaviorTree>
    <!-- ////////// -->
    <BehaviorTree ID="DustingSubtree">
        <ForceSuccess>
            <Sequence name="集尘子树">
                <Sequence name="集尘条件判断">
                    <Condition ID="IsDusting" name="判断集尘是否开启"/>
                    <Inverter>
                        <Sequence>
                            <Condition ID="Is_error_happen" behavior="NULL" name="机器是否发生异常"/>
                            <Action ID="Workstation_start_finish" name="集尘异常结束"/>
                        </Sequence>
                    </Inverter>
                </Sequence>
                <Action ID="Workstation_task_pub" pub_name="start_dusting"/>
                <Sequence name="开始集尘">
                    <Decorator ID="MyDelayNode" delay_msec="4000" name="等待语音播报结束">
                        <Action ID="Run_log_pub" application_name="software/workstation" float_log_type="" float_log_value="" log_type="" log_value="" pipeline_log_type="info" pipeline_log_value="supply/start_dust_collection"/>
                    </Decorator>
                    <Action ID="Workstation_blackboard" name="开始集尘" output_key1="dusting_behavior" output_key2="robot_dusting_service" output_key3="dusting_timeout" output_key4="workstation_dusting_service" value1="dusting" value2="NULL" value3="20" value4="/workstation/dusting"/>
                    <Decorator ID="PubStateLog" application_name="software/workstation" expect="FAILURE" float_log_type="" float_log_value="" log_type="warn" log_value="[工作站]:调用集尘服务失败" pipeline_log_type="" pipeline_log_value="">
                        <SubTree ID="ServiceCallSubtree" __autoremap="true" __shared_blackboard="false" behavior="{dusting_behavior}" robot_service_name="{robot_dusting_service}" status="1" workstation_service_name="{workstation_dusting_service}"/>
                    </Decorator>
                </Sequence>
                <Parallel failure_threshold="1" success_threshold="1">
                    <Decorator ID="KeepRunning" is_failure_return="false" is_success_return="true" name="集尘是否异常">
                        <Condition ID="Is_error_happen" behavior="{dusting_behavior}"/>
                    </Decorator>
                    <Sequence>
                        <Decorator ID="KeepRunning" is_failure_return="false" is_success_return="true" name="等待集尘完成">
                            <Condition ID="Workstation_is_timeout" behavior="{dusting_behavior}" timeout="{dusting_timeout}"/>
                        </Decorator>
                        <Action ID="Dust_update_pub" name="Update dust_box status" value="0.0"/>
                    </Sequence>
                </Parallel>
                <SubTree ID="ServiceCallSubtree" __autoremap="true" __shared_blackboard="false" behavior="{dusting_behavior}" robot_service_name="{robot_dusting_service}" status="0" workstation_service_name="{workstation_dusting_service}"/>
            </Sequence>
        </ForceSuccess>
    </BehaviorTree>
    <!-- ////////// -->
    <BehaviorTree ID="HepaDetectSubtree">
        <ForceSuccess>
            <ReactiveSequence name="海帕检测是否堵塞">
                <Inverter>
                    <Condition ID="Is_error_happen" behavior="NULL"/>
                </Inverter>
                <Inverter>
                    <Condition ID="Is_component_error" component_name="fan"/>
                </Inverter>
                <Action ID="Single_Component_Ctrl" component_name="fan" control_gap_sec="1" control_sec="10" level="0.9" name="开始组件控制"/>
                <Decorator ID="MyDelayNode" delay_msec="8000" name="海帕检测结束后延时8s再执行后续内容">
                    <AlwaysSuccess/>
                </Decorator>
            </ReactiveSequence>
        </ForceSuccess>
    </BehaviorTree>
    <!-- ////////// -->
    <BehaviorTree ID="ManualSupplySubtree">
        <Sequence>
            <Action ID="Start_charge" data="false" name="暂停充电"/>
            <Action ID="Get_manual_ctrl_process" manual_ctrl_name="{manual_ctrl_name}" name="获取手动需求行为" open="{manual_ctrl_open}"/>
            <Switch2 case_1="manual_add_water" case_2="manual_drain_water" name="选择手动加水或排水" variable="{manual_ctrl_name}">
                <Sequence name="手动加水流程">
                    <SubTree ID="AddWaterSubtree" __autoremap="true" __shared_blackboard="false" ctrl_mode="manual" water_level_is_change="{is_change}"/>
                    <SubTree ID="AddCleanFluidSubtree" __autoremap="true" __shared_blackboard="false" water_level_is_change="{is_change}"/>
                    <Action ID="manual_ctrl_finish" ctrl_name="{manual_ctrl_name}"/>
                </Sequence>
                <Sequence name="手动排水流程">
                    <SubTree ID="RolltubeSelfCleanSubtree" __autoremap="true" __shared_blackboard="false" ctrl_mode="manual" type="deep"/>
                    <Action ID="manual_ctrl_finish" ctrl_name="{manual_ctrl_name}"/>
                </Sequence>
                <AlwaysSuccess/>
            </Switch2>
        </Sequence>
    </BehaviorTree>
    <!-- ////////// -->
    <BehaviorTree ID="RolltubeSelfCleanProcess">
        <Sequence>
            <Sequence name="开始自清洁">
                <Action ID="Workstation_blackboard" name="开始自清洁" output_key1="selfclean_behavior" output_key2="robot_selfclean_service" output_key3="selfclean_timeout" output_key4="workstation_selfclean_service" value1="rolltube_selfclean" value2="/robot/rolltube_selfclean" value3="{keeptime}" value4="NULL"/>
                <Decorator ID="PubStateLog" application_name="software/robot" expect="FAILURE" float_log_type="" float_log_value="" log_type="warn" log_value="[本体]:调用自清洁服务失败" pipeline_log_type="" pipeline_log_value="">
                    <SubTree ID="ServiceCallSubtree" __autoremap="true" __shared_blackboard="false" behavior="{selfclean_behavior}" robot_service_name="{robot_selfclean_service}" status="1" workstation_service_name="{workstation_selfclean_service}"/>
                </Decorator>
            </Sequence>
            <Parallel failure_threshold="1" name="自清洁子流程" success_threshold="1">
                <Decorator ID="KeepRunning" is_failure_return="false" is_success_return="true" name="自清洁异常检测">
                    <Fallback>
                        <Condition ID="Is_error_happen" behavior="{selfclean_behavior}"/>
                        <Condition ID="Is_sewage_water_more" value="1.0"/>
                        <Condition ID="Is_clean_water_less" value="0.0"/>
                        <Decorator ID="PubStateLog" application_name="software/selfclean_error" expect="SUCCESS" float_log_type="" float_log_value="" log_type="error" log_value="水槽不在位，跳过自清洁流程" pipeline_log_type="" pipeline_log_value="">
                            <Condition ID="Is_component_error" component_name="sewage_water_groove"/>
                        </Decorator>
                    </Fallback>
                </Decorator>
                <Decorator ID="KeepRunning" is_failure_return="false" is_success_return="true" name="等待自清洁完成">
                    <Condition ID="Workstation_is_timeout" behavior="{selfclean_behavior}" timeout="{selfclean_timeout}"/>
                </Decorator>
            </Parallel>
            <SubTree ID="ServiceCallSubtree" __autoremap="true" __shared_blackboard="false" behavior="{selfclean_behavior}" robot_service_name="{robot_selfclean_service}" status="0" workstation_service_name="{workstation_selfclean_service}"/>
            <Sequence name="自清洁后行为">
                <ForceSuccess>
                    <ReactiveSequence name="自清洁后延长污水泵开启时间为25s">
                        <Inverter>
                            <Condition ID="Is_error_happen" behavior="NULL"/>
                        </Inverter>
                        <Action ID="Single_Component_Ctrl" component_name="filter_water_pump" control_gap_sec="1" control_sec="25" level="1.0"/>
                    </ReactiveSequence>
                </ForceSuccess>
                <SubTree ID="DrainWaterSubtree" __autoremap="true" __shared_blackboard="false" ctrl_mode="auto"/>
            </Sequence>
        </Sequence>
    </BehaviorTree>
    <!-- ////////// -->
    <BehaviorTree ID="RolltubeSelfCleanSubtree">
        <ForceSuccess>
            <Sequence name="自清洁流程子树">
                <Inverter>
                    <Fallback name="自清洁条件判断">
                        <Condition ID="Is_clean_water_less" name="清水箱是否为空" value="0.0"/>
                        <Condition ID="Is_sewage_water_more" name="机器污水箱是否为满" value="1.0"/>
                        <Sequence>
                            <Condition ID="Is_error_happen" behavior="NULL"/>
                            <Action ID="Workstation_start_finish" name="自清洁异常结束"/>
                        </Sequence>
                        <Decorator ID="PubStateLog" application_name="software/workstation" expect="SUCCESS" float_log_type="" float_log_value="" log_type="error" log_value="水槽不在位，跳过自清洁流程" pipeline_log_type="" pipeline_log_value="">
                            <Condition ID="Is_component_error" component_name="sewage_water_groove" name="判断本体集水槽是否在位"/>
                        </Decorator>
                    </Fallback>
                </Inverter>
                <Action ID="VoiceSwitchBlackboard" auto_pub_end_voice="sewagebox_end" auto_pub_start_voice="sewagebox_start" ctrl_mode="{ctrl_mode}" manual_pub_end_voice="manual_drain_water_end" manual_pub_start_voice="manual_drain_water_start" name="获取不同模式语音播报内容" voice_pub_end="{pub_end}" voice_pub_start="{pub_start}"/>
                <Switch2 case_1="deep" case_2="normal" name="模式选择" variable="{type}">
                    <Sequence name="深度自清洁流程">
                        <Action ID="Run_log_pub" application_name="software/workstation" float_log_type="" float_log_value="" log_type="info" log_value="本体开始深度自清洁" pipeline_log_type="info" pipeline_log_value="supply/start_deep_self_clean"/>
                        <Action ID="Workstation_task_pub" name="语音播报" pub_name="{pub_start}"/>
                        <Repeat num_cycles="2">
                            <SubTree ID="RolltubeSelfCleanProcess" __autoremap="true" __shared_blackboard="false" keeptime="300"/>
                        </Repeat>
                        <Action ID="Workstation_task_pub" name="语音播报" pub_name="{pub_end}"/>
                        <Action ID="Run_log_pub" application_name="software/workstation" float_log_type="" float_log_value="" log_type="info" log_value="本体结束深度自清洁" pipeline_log_type="" pipeline_log_value=""/>
                    </Sequence>
                    <Sequence name="普通自清洁流程">
                        <Action ID="Run_log_pub" application_name="software/workstation" float_log_type="" float_log_value="" log_type="info" log_value="开始滚筒自清洁" pipeline_log_type="info" pipeline_log_value="supply/start_self_clean"/>
                        <Action ID="Workstation_task_pub" name="语音播报" pub_name="selfclean_start"/>
                        <SubTree ID="RolltubeSelfCleanProcess" __autoremap="true" __shared_blackboard="false" keeptime="40"/>
                    </Sequence>
                    <AlwaysSuccess name="未知模式"/>
                </Switch2>
            </Sequence>
        </ForceSuccess>
    </BehaviorTree>
    <!-- ////////// -->
    <BehaviorTree ID="ServiceCallSubtree">
        <Sequence name="调用服务流程">
            <Action ID="Process_init" behavior="{behavior}" name="行为初始化"/>
            <Switch2 case_1="1" case_2="0" name="开启or关闭服务" variable="{status}">
                <IfThenElse name="开启服务流程">
                    <Sequence>
                        <RetryUntilSuccessful num_attempts="5">
                            <Action ID="Service_call" data="1" service_name="{robot_service_name}"/>
                        </RetryUntilSuccessful>
                        <RetryUntilSuccessful num_attempts="5">
                            <Action ID="Service_call" data="1" service_name="{workstation_service_name}"/>
                        </RetryUntilSuccessful>
                    </Sequence>
                    <AlwaysSuccess name="服务调用成功"/>
                    <Sequence name="开启服务失败异常处理流程">
                        <ForceSuccess>
                            <RetryUntilSuccessful num_attempts="5">
                                <Action ID="Service_call" data="0" service_name="{robot_service_name}"/>
                            </RetryUntilSuccessful>
                        </ForceSuccess>
                        <ForceSuccess>
                            <RetryUntilSuccessful num_attempts="5">
                                <Action ID="Service_call" data="0" service_name="{workstation_service_name}"/>
                            </RetryUntilSuccessful>
                        </ForceSuccess>
                    </Sequence>
                </IfThenElse>
                <Sequence name="关闭服务流程">
                    <ForceSuccess>
                        <RetryUntilSuccessful num_attempts="5">
                            <Action ID="Service_call" data="0" service_name="{robot_service_name}"/>
                        </RetryUntilSuccessful>
                    </ForceSuccess>
                    <ForceSuccess>
                        <RetryUntilSuccessful num_attempts="5">
                            <Action ID="Service_call" data="0" service_name="{workstation_service_name}"/>
                        </RetryUntilSuccessful>
                    </ForceSuccess>
                </Sequence>
                <AlwaysSuccess/>
            </Switch2>
        </Sequence>
    </BehaviorTree>
    <!-- ////////// -->
    <BehaviorTree ID="SewageLevelDoubleCheck">
        <IfThenElse name="排污后污水箱水位检测">
            <Condition ID="Is_sewage_water_more" name="排污后污水量是否为满" value="1.0"/>
            <Sequence name="污水量满，进入二次水位校验">
                <ForceSuccess>
                    <ReactiveSequence name="开启污水泵，检测污水箱水位状态">
                        <Inverter>
                            <Condition ID="Is_error_happen" behavior="NULL"/>
                        </Inverter>
                        <Action ID="Single_Component_Ctrl" component_name="filter_water_pump" control_gap_sec="1" control_sec="15" level="1.0"/>
                    </ReactiveSequence>
                </ForceSuccess>
                <IfThenElse>
                    <Condition ID="Is_sewage_water_more" name="污水量是否还为满" value="1.0"/>
                    <Action ID="Run_log_pub" application_name="workstation_bt/sewage_water_error" float_log_type="" float_log_value="" log_type="error" log_value="排污异常，请检查排污机构是否能够正常排污" pipeline_log_type="error" pipeline_log_value="supply/sewage_water_timeout"/>
                    <AlwaysSuccess name="二次校验后水位不为满，排污传感器异常"/>
                </IfThenElse>
            </Sequence>
            <AlwaysSuccess name="排污后污水量不为满"/>
        </IfThenElse>
    </BehaviorTree>
    <!-- ////////// -->
    <BehaviorTree ID="SewageTankFullSubtree">
        <Sequence>
            <Condition ID="Is_sewage_tank_full"/>
            <Sequence name="过滤水槽满异常处理">
                <ForceSuccess name="关闭本体排污">
                    <RetryUntilSuccessful num_attempts="2">
                        <Action ID="Service_call" data="0" name="关闭排污服务" service_name="{robot_service_name}"/>
                    </RetryUntilSuccessful>
                </ForceSuccess>
                <Decorator ID="MyDelayNode" delay_msec="5000" name="暂停5s后判断过滤水槽水位">
                    <IfThenElse name="过滤水槽水位判断">
                        <Condition ID="Is_sewage_tank_full"/>
                        <Action ID="Run_log_pub" application_name="workstation/sewage_pump_error" float_log_type="" float_log_value="" log_type="error" log_value="工作站过滤水槽满，抽污功能异常" name="过滤水槽满，发布异常" pipeline_log_type="" pipeline_log_value=""/>
                        <ForceFailure name="重新开启本体排污">
                            <RetryUntilSuccessful num_attempts="2">
                                <Action ID="Service_call" data="1" service_name="{robot_service_name}"/>
                            </RetryUntilSuccessful>
                        </ForceFailure>
                    </IfThenElse>
                </Decorator>
            </Sequence>
        </Sequence>
    </BehaviorTree>
    <!-- ////////// -->
    <BehaviorTree ID="WholeProcessSupplySubtree">
        <Sequence>
            <Action ID="GetLastTaskType" LastTaskType="{task_type}" name="获取补给前任务类型"/>
            <Switch2 case_1="floor_washing" case_2="vacuuming" name="判断需要执行的流程" variable="{task_type}">
                <Sequence name="全流程补给">
                    <SubTree ID="HepaDetectSubtree"/>
                    <SubTree ID="AddDrainParallelSubtree" __autoremap="false" __shared_blackboard="false"/>
                    <IfThenElse name="自清洁流程">
                        <Condition ID="Is_timeout" hour="24" min="0" msec="0" name="是否满足24H" sec="0"/>
                        <SubTree ID="RolltubeSelfCleanSubtree" __autoremap="true" __shared_blackboard="false" ctrl_mode="auto" type="deep"/>
                        <SubTree ID="RolltubeSelfCleanSubtree" __autoremap="true" __shared_blackboard="false" ctrl_mode="auto" type="normal"/>
                    </IfThenElse>
                    <SubTree ID="AddWaterSubtree" __autoremap="true" __shared_blackboard="false" ctrl_mode="auto" water_level_is_change="{is_change}"/>
                    <SubTree ID="AddCleanFluidSubtree" __autoremap="true" __shared_blackboard="false" water_level_is_change="{is_change}"/>
                    <SubTree ID="DustingSubtree"/>
                    <Action ID="Start_charge" data="true" name="开始充电"/>
                    <Action ID="Workstation_start_finish" name="自清洁与补给流程结束，等待充电完成后开始烘干"/>
                    <ReactiveSequence>
                        <Inverter>
                            <Condition ID="Is_error_happen" behavior="NULL"/>
                        </Inverter>
                        <Inverter>
                            <Condition ID="Is_workstation_start"/>
                        </Inverter>
                        <Action ID="Waiting_charge_state" percentage="100" power_supply_status="3" state="WaitCancel"/>
                    </ReactiveSequence>
                    <SubTree ID="DryingSubtree"/>
                </Sequence>
                <Sequence name="只集尘">
                    <SubTree ID="HepaDetectSubtree"/>
                    <SubTree ID="DustingSubtree" name=""/>
                    <Action ID="Workstation_start_finish"/>
                </Sequence>
                <Action ID="Workstation_start_finish" name="不需要执行流程"/>
            </Switch2>
        </Sequence>
    </BehaviorTree>
    <!-- ////////// -->
    <TreeNodesModel>
        <SubTree ID="AddCleanFluidSubtree">
            <input_port name="__autoremap"/>
            <input_port default="false" name="__shared_blackboard">If false (default), the Subtree has an isolated blackboard and needs port remapping</input_port>
            <input_port name="water_level_is_change"/>
        </SubTree>
        <SubTree ID="AddDrainParallelSubtree">
            <input_port name="__autoremap"/>
            <input_port default="false" name="__shared_blackboard">If false (default), the Subtree has an isolated blackboard and needs port remapping</input_port>
        </SubTree>
        <SubTree ID="AddWaterSubtree">
            <input_port name="__autoremap"/>
            <input_port default="false" name="__shared_blackboard">If false (default), the Subtree has an isolated blackboard and needs port remapping</input_port>
            <input_port name="ctrl_mode"/>
            <output_port default="{is_change}" name="water_level_is_change"/>
        </SubTree>
        <Action ID="CalcAddCleanFluidTime">
            <input_port default="4000" name="clean_capacity">清水箱容积/mL</input_port>
            <output_port name="clean_fluid"/>
            <input_port default="{clean_fluid_ratio}" name="clean_fluid_ratio">清洁液浓度</input_port>
            <input_port default="350" name="clean_fluid_traffic">清水泵流量ml/min</input_port>
        </Action>
        <SubTree ID="CalcAddFluidTimeProc">
            <input_port name="__autoremap"/>
            <input_port default="false" name="__shared_blackboard">If false (default), the Subtree has an isolated blackboard and needs port remapping</input_port>
            <output_port default="{clean_fluid_time}" name="add_clean_fluid_time"/>
            <input_port default="{ratio}" name="clean_fluid_ratio"/>
        </SubTree>
        <SubTree ID="DrainWaterSubtree">
            <input_port name="__autoremap"/>
            <input_port default="false" name="__shared_blackboard">If false (default), the Subtree has an isolated blackboard and needs port remapping</input_port>
            <input_port name="ctrl_mode"/>
        </SubTree>
        <SubTree ID="DryingSubtree"/>
        <SubTree ID="DuringTaskSupplySubtree">
            <input_port name="__autoremap"/>
            <input_port default="false" name="__shared_blackboard">If false (default), the Subtree has an isolated blackboard and needs port remapping</input_port>
        </SubTree>
        <Action ID="Dust_update_pub">
            <input_port name="value"/>
        </Action>
        <SubTree ID="DustingSubtree"/>
        <Action ID="Force_set_clean_fluid">
            <output_port name="clean_fluid">清洁液添加时间</output_port>
            <input_port default="{clean_fluid_ratio}" name="clean_fluid_ratio">清洁液浓度</input_port>
        </Action>
        <Action ID="GetLastTaskType">
            <output_port default="{task_type}" name="LastTaskType"/>
        </Action>
        <Action ID="Get_clean_water_position">
            <output_port default="{water_level}" name="water_current_level"/>
        </Action>
        <Action ID="Get_manual_ctrl_process">
            <output_port name="manual_ctrl_name">控制名称</output_port>
            <output_port name="open">控制开关</output_port>
        </Action>
        <Action ID="Get_process_type">
            <input_port default="{process}" name="process"/>
        </Action>
        <SubTree ID="HepaDetectSubtree"/>
        <Condition ID="IsAddCleanFluid">
            <output_port default="{ratio}" name="clean_fluid_ratio"/>
        </Condition>
        <Condition ID="IsDrying"/>
        <Condition ID="IsDusting"/>
        <Condition ID="IsExpected">
            <input_port name="expected"/>
            <input_port name="variable"/>
        </Condition>
        <Condition ID="Is_behavior_timeout">
            <input_port name="behavior">行为是否超时</input_port>
        </Condition>
        <Condition ID="Is_clean_water_calibrate"/>
        <Condition ID="Is_clean_water_less">
            <input_port name="value"/>
        </Condition>
        <Condition ID="Is_clean_water_more">
            <input_port name="value"/>
        </Condition>
        <Condition ID="Is_clean_water_volume_change">
            <output_port default="{water_level_is_change}" name="is_change"/>
            <input_port default="{water_level}" name="last_water_level"/>
        </Condition>
        <Condition ID="Is_component_error">
            <input_port name="component_name"/>
        </Condition>
        <Condition ID="Is_error_happen">
            <input_port default="NULL" name="behavior"/>
        </Condition>
        <Condition ID="Is_need_add_clean_fluid"/>
        <Condition ID="Is_need_start">
            <input_port default="{behavior}" name="behavior"/>
        </Condition>
        <Condition ID="Is_sewage_pump_error"/>
        <Condition ID="Is_sewage_tank_full"/>
        <Condition ID="Is_sewage_water_less">
            <input_port name="value"/>
        </Condition>
        <Condition ID="Is_sewage_water_more">
            <input_port name="value"/>
        </Condition>
        <Condition ID="Is_timeout">
            <input_port name="hour"/>
            <input_port name="min"/>
            <input_port name="msec"/>
            <input_port name="sec"/>
        </Condition>
        <Condition ID="Is_workstation_start"/>
        <Decorator ID="KeepRunning">
            <input_port name="is_failure_return"/>
            <input_port name="is_success_return"/>
        </Decorator>
        <SubTree ID="ManualSupplySubtree">
            <input_port name="__autoremap"/>
            <input_port default="false" name="__shared_blackboard">If false (default), the Subtree has an isolated blackboard and needs port remapping</input_port>
        </SubTree>
        <Decorator ID="MyDelayNode">
            <input_port name="delay_msec">设置延时时间</input_port>
        </Decorator>
        <Action ID="Process_init">
            <input_port default="{behavior}" name="behavior"/>
        </Action>
        <Action ID="Process_return">
            <input_port default="{behavior}" name="behavior"/>
            <input_port default="{ret}" name="ret"/>
        </Action>
        <Action ID="Process_type_finish">
            <input_port name="type"/>
        </Action>
        <Decorator ID="PubStateLog">
            <input_port name="application_name"/>
            <input_port name="expect"/>
            <input_port name="float_log_type"/>
            <input_port name="float_log_value"/>
            <input_port name="log_type"/>
            <input_port name="log_value"/>
            <input_port name="pipeline_log_type"/>
            <input_port name="pipeline_log_value"/>
        </Decorator>
        <SubTree ID="RolltubeSelfCleanProcess">
            <input_port name="__autoremap"/>
            <input_port default="false" name="__shared_blackboard">If false (default), the Subtree has an isolated blackboard and needs port remapping</input_port>
            <input_port name="keeptime"/>
        </SubTree>
        <SubTree ID="RolltubeSelfCleanSubtree">
            <input_port name="__autoremap"/>
            <input_port default="false" name="__shared_blackboard">If false (default), the Subtree has an isolated blackboard and needs port remapping</input_port>
            <input_port name="ctrl_mode"/>
            <input_port name="type"/>
        </SubTree>
        <Action ID="Run_log_pub">
            <input_port default="software/workstation" name="application_name"/>
            <input_port name="float_log_type"/>
            <input_port name="float_log_value"/>
            <input_port name="log_type"/>
            <input_port name="log_value"/>
            <input_port name="pipeline_log_type"/>
            <input_port name="pipeline_log_value"/>
        </Action>
        <SubTree ID="ServiceCallSubtree">
            <input_port name="__autoremap"/>
            <input_port default="false" name="__shared_blackboard">If false (default), the Subtree has an isolated blackboard and needs port remapping</input_port>
            <input_port default="behavior" name="behavior"/>
            <input_port default="robot_service_name" name="robot_service_name"/>
            <input_port name="status"/>
            <input_port default="workstation_service_name" name="workstation_service_name"/>
        </SubTree>
        <Action ID="Service_call">
            <input_port name="data"/>
            <input_port name="service_name"/>
        </Action>
        <Action ID="SetBlackboard2">
            <output_port default="{output_key1}" name="output_key_1"/>
            <output_port default="{output_key2}" name="output_key_2"/>
            <input_port default="{value1}" name="value_1"/>
            <input_port default="{value2}" name="value_2"/>
        </Action>
        <Action ID="SetBlackboard3">
            <output_port default="{output_key1}" name="output_key_1"/>
            <output_port default="{output_key2}" name="output_key_2"/>
            <output_port default="{output_key3}" name="output_key_3"/>
            <input_port default="{value1}" name="value_1"/>
            <input_port default="{value2}" name="value_2"/>
            <input_port default="{value3}" name="value_3"/>
        </Action>
        <Action ID="SetBlackboard4">
            <output_port default="{output_key1}" name="output_key_1"/>
            <output_port default="{output_key2}" name="output_key_2"/>
            <output_port default="{output_key3}" name="output_key_3"/>
            <output_port default="{output_key4}" name="output_key_4"/>
            <input_port default="{value1}" name="value_1"/>
            <input_port default="{value2}" name="value_2"/>
            <input_port default="{value3}" name="value_3"/>
            <input_port default="{value4}" name="value_4"/>
        </Action>
        <SubTree ID="SewageLevelDoubleCheck">
            <input_port name="__autoremap"/>
            <input_port default="false" name="__shared_blackboard">If false (default), the Subtree has an isolated blackboard and needs port remapping</input_port>
        </SubTree>
        <SubTree ID="SewageTankFullSubtree">
            <input_port name="__autoremap"/>
            <input_port default="false" name="__shared_blackboard">If false (default), the Subtree has an isolated blackboard and needs port remapping</input_port>
            <input_port default="{service}" name="robot_service_name"/>
        </SubTree>
        <Action ID="Single_Component_Ctrl">
            <input_port name="component_name"/>
            <input_port name="control_gap_sec"/>
            <input_port name="control_sec"/>
            <input_port name="level"/>
        </Action>
        <Action ID="Start_charge">
            <input_port name="data"/>
        </Action>
        <Action ID="Update_Robot_State"/>
        <Action ID="VoiceSwitchBlackboard">
            <input_port name="auto_pub_end_voice"/>
            <input_port name="auto_pub_start_voice"/>
            <input_port default="{ctrl_mode}" name="ctrl_mode"/>
            <input_port name="manual_pub_end_voice"/>
            <input_port name="manual_pub_start_voice"/>
            <output_port default="{pub_end}" name="voice_pub_end"/>
            <output_port default="{pub_start}" name="voice_pub_start"/>
        </Action>
        <Action ID="Waiting">
            <input_port default="-1" name="wait_time"/>
        </Action>
        <Action ID="Waiting_charge_state">
            <input_port name="percentage">Use -1 to ignore the judgment condition.</input_port>
            <input_port name="power_supply_status">Use -1 to ignore the judgment condition.</input_port>
            <input_port name="state"/>
        </Action>
        <SubTree ID="WholeProcessSupplySubtree">
            <input_port name="__autoremap"/>
            <input_port default="false" name="__shared_blackboard">If false (default), the Subtree has an isolated blackboard and needs port remapping</input_port>
        </SubTree>
        <Action ID="Workstation_blackboard">
            <inout_port default="behavior" name="output_key1"/>
            <inout_port default="robot_service_name" name="output_key2"/>
            <inout_port default="timeout" name="output_key3"/>
            <inout_port default="workstation_service_name" name="output_key4"/>
            <input_port name="value1"/>
            <input_port name="value2"/>
            <input_port name="value3"/>
            <input_port name="value4"/>
        </Action>
        <Condition ID="Workstation_is_timeout">
            <input_port default="{behavior}" name="behavior"/>
            <input_port default="{timeout}" name="timeout"/>
        </Condition>
        <Action ID="Workstation_start_finish"/>
        <Action ID="Workstation_task_pub">
            <input_port name="pub_name"/>
        </Action>
        <Action ID="manual_ctrl_finish">
            <input_port default="{manual_ctrl_name}" name="ctrl_name"/>
        </Action>
    </TreeNodesModel>
    <!-- ////////// -->
</root>
