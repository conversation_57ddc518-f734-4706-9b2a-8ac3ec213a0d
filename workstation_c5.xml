<?xml version="1.0"?>
<root main_tree_to_execute="BehaviorTree">
    <!-- ////////// -->
    <BehaviorTree ID="BehaviorTree">
        <Sequence>
            <Action ID="Start_charge" data="true"/>
            <Condition ID="Is_change_to" is_in_pile="1"/>
            <ReactiveSequence>
                <Inverter>
                    <Condition ID="Is_change_to" is_in_pile="0"/>
                </Inverter>
                <Action ID="Waiting_charge_state" name="等待电池达到15，且进入充电状态" percentage="15" power_supply_status="1" state="Charging"/>
            </ReactiveSequence>
            <Action ID="Start_charge" data="false" name="暂停充电"/>
            <Action ID="Waiting_charge_state" name="等待进入暂停充电" percentage="15" power_supply_status="3" state="PauseCharge"/>
            <Action ID="Get_process_type" name="判断回桩后执行的流程" process="{process}"/>
            <Switch2 case_1="default" case_2="water_cycle" name="如何回桩" variable="{process}">
                <Sequence>
                    <Action ID="Workstation_blackboard" name="开始集尘" output_key1="behavior" output_key2="robot_service_name" output_key3="timeout" output_key4="workstation_service_name" value1="dusting" value2="NULL" value3="20" value4="/workstation/dusting"/>
                    <SubTree ID="Sub_process" behavior="behavior" robot_service_name="robot_service_name" timeout="timeout" workstation_service_name="workstation_service_name"/>
                    <Action ID="Start_charge" data="true" name="取消暂停充电"/>
                </Sequence>
                <Sequence>
                    <Action ID="Workstation_blackboard" name="开始排污" output_key1="behavior" output_key2="robot_service_name" output_key3="timeout" output_key4="workstation_service_name" value1="sewage_water" value2="/robot/sewage_water" value3="150" value4="/workstation/sewage_water"/>
                    <SubTree ID="Sub_process" behavior="behavior" robot_service_name="robot_service_name" timeout="timeout" workstation_service_name="workstation_service_name"/>
                    <IfThenElse name="是否需要加水">
                        <Fallback>
                            <Inverter>
                                <RetryUntilSuccessful num_attempts="2">
                                    <Action ID="Get_workstation_info" clean_capacity="5000" clean_fluid="{clean_fluid}" clean_fluid_traffic="600" name="获取加清洁液信息"/>
                                </RetryUntilSuccessful>
                            </Inverter>
                            <Condition ID="Is_clean_water_less" name="清水是否小于阈值" value="0.15"/>
                        </Fallback>
                        <Sequence>
                            <Action ID="Workstation_blackboard" name="开始加水" output_key1="behavior" output_key2="robot_service_name" output_key3="timeout" output_key4="workstation_service_name" value1="add_clean_water" value2="/robot/add_clean_water" value3="300" value4="/workstation/add_clean_water"/>
                            <SubTree ID="Sub_process" behavior="behavior" robot_service_name="robot_service_name" timeout="timeout" workstation_service_name="workstation_service_name"/>
                            <Action ID="Workstation_blackboard" name="开始加清洁液" output_key1="behavior" output_key2="robot_service_name" output_key3="timeout" output_key4="workstation_service_name" value1="add_clean_fluid" value2="NULL" value3="{clean_fluid}" value4="/workstation/add_clean_fluid"/>
                            <SubTree ID="Sub_process" behavior="behavior" robot_service_name="robot_service_name" timeout="timeout" workstation_service_name="workstation_service_name"/>
                            <IfThenElse name="是否需要深度自清洁">
                                <ReactiveFallback>
                                    <Condition ID="Is_error_happen" behavior="NULL"/>
                                    <Condition ID="Is_timeout" hour="24" min="0" msec="0" name="运行是否超过指定时间" sec="0"/>
                                </ReactiveFallback>
                                <Sequence name="开始深度自清洁">
                                    <Inverter>
                                        <Condition ID="Is_error_happen" behavior="NULL"/>
                                    </Inverter>
                                    <Action ID="Run_log_pub" application_name="software/workstation" log_type="info" log_value="本体开始深度自清洁"/>
                                    <Repeat num_cycles="2">
                                        <Sequence name="水箱自清洁">
                                            <Action ID="Workstation_blackboard" name="滚筒自清洁" output_key1="behavior" output_key2="robot_service_name" output_key3="timeout" output_key4="workstation_service_name" value1="rolltube_selfclean" value2="/robot/rolltube_selfclean" value3="300" value4="NULL"/>
                                            <SubTree ID="Sub_process" behavior="behavior" robot_service_name="robot_service_name" timeout="timeout" workstation_service_name="workstation_service_name"/>
                                            <Delay delay_msec="2000" name="延迟2s后开始排污">
                                                <Action ID="Workstation_blackboard" name="开始排污" output_key1="behavior" output_key2="robot_service_name" output_key3="timeout" output_key4="workstation_service_name" value1="sewage_water" value2="/robot/sewage_water" value3="150" value4="/workstation/sewage_water"/>
                                            </Delay>
                                            <SubTree ID="Sub_process" behavior="behavior" robot_service_name="robot_service_name" timeout="timeout" workstation_service_name="workstation_service_name"/>
                                        </Sequence>
                                    </Repeat>
                                    <Action ID="Run_log_pub" application_name="software/workstation" log_type="info" log_value="本体结束深度自清洁"/>
                                </Sequence>
                                <Sequence name="滚筒自清洁">
                                    <Action ID="Workstation_blackboard" name="开始滚筒自清洁" output_key1="behavior" output_key2="robot_service_name" output_key3="timeout" output_key4="workstation_service_name" value1="rolltube_selfclean" value2="/robot/rolltube_selfclean" value3="40" value4="NULL"/>
                                    <SubTree ID="Sub_process" behavior="behavior" robot_service_name="robot_service_name" timeout="timeout" workstation_service_name="workstation_service_name"/>
                                </Sequence>
                            </IfThenElse>
                        </Sequence>
                        <Sequence name="滚筒自清洁">
                            <Action ID="Run_log_pub" application_name="software/workstation" log_type="info" log_value="配置添加清洁液，水没用完不加水"/>
                            <Action ID="Workstation_blackboard" name="开始滚筒自清洁" output_key1="behavior" output_key2="robot_service_name" output_key3="timeout" output_key4="workstation_service_name" value1="rolltube_selfclean" value2="/robot/rolltube_selfclean" value3="40" value4="NULL"/>
                            <SubTree ID="Sub_process" behavior="behavior" robot_service_name="robot_service_name" timeout="timeout" workstation_service_name="workstation_service_name"/>
                        </Sequence>
                    </IfThenElse>
                    <Sequence name="自清洁结束后行为">
                        <ForceSuccess>
                            <Sequence name="是否加水">
                                <Condition ID="Is_clean_water_less" value="0.15"/>
                                <Action ID="Run_log_pub" application_name="software/workstation" log_type="info" log_value="自清洁后水量低，开始加水"/>
                                <Action ID="Workstation_blackboard" name="开始加水" output_key1="behavior" output_key2="robot_service_name" output_key3="timeout" output_key4="workstation_service_name" value1="add_clean_water" value2="/robot/add_clean_water" value3="300" value4="/workstation/add_clean_water"/>
                                <SubTree ID="Sub_process" behavior="behavior" robot_service_name="robot_service_name" timeout="timeout" workstation_service_name="workstation_service_name"/>
                                <Action ID="Workstation_blackboard" name="开始加清洁液" output_key1="behavior" output_key2="robot_service_name" output_key3="timeout" output_key4="workstation_service_name" value1="add_clean_fluid" value2="NULL" value3="{clean_fluid}" value4="/workstation/add_clean_fluid"/>
                                <SubTree ID="Sub_process" behavior="behavior" robot_service_name="robot_service_name" timeout="timeout" workstation_service_name="workstation_service_name"/>
                            </Sequence>
                        </ForceSuccess>
                        <Action ID="Workstation_blackboard" name="开始集尘" output_key1="behavior" output_key2="robot_service_name" output_key3="timeout" output_key4="workstation_service_name" value1="dusting" value2="NULL" value3="20" value4="/workstation/dusting"/>
                        <SubTree ID="Sub_process" behavior="behavior" robot_service_name="robot_service_name" timeout="timeout" workstation_service_name="workstation_service_name"/>
                        <Action ID="Start_charge" data="true" name="取消暂停充电"/>
                        <ReactiveSequence>
                            <Inverter>
                                <Condition ID="Is_error_happen" behavior="NULL"/>
                            </Inverter>
                            <Action ID="Waiting_charge_state" name="等待电池充满" percentage="100" power_supply_status="3" state="WaitCancel"/>
                        </ReactiveSequence>
                        <Action ID="Workstation_blackboard" name="开始烘干" output_key1="behavior" output_key2="robot_service_name" output_key3="timeout" output_key4="workstation_service_name" value1="drying" value2="/robot/drying" value3="7200" value4="/workstation/drying"/>
                        <SubTree ID="Sub_process" behavior="behavior" robot_service_name="robot_service_name" timeout="timeout" workstation_service_name="workstation_service_name"/>
                    </Sequence>
                </Sequence>
                <AlwaysSuccess/>
            </Switch2>
        </Sequence>
    </BehaviorTree>
    <!-- ////////// -->
    <BehaviorTree ID="Sub_process">
        <Sequence>
            <Action ID="Process_init" behavior="{behavior}"/>
            <IfThenElse>
                <ReactiveFallback>
                    <Condition ID="Is_error_happen" behavior="NULL"/>
                    <Switch3 case_1="add_clean_water" case_2="sewage_water" case_3="rolltube_selfclean" variable="{behavior}">
                        <Condition ID="Is_clean_water_more" value="1.0"/>
                        <Condition ID="Is_sewage_water_less" value="0"/>
                        <ReactiveFallback>
                            <Condition ID="Is_sewage_water_more" value="1.0"/>
                            <Condition ID="Is_clean_water_less" value="0"/>
                        </ReactiveFallback>
                        <AlwaysFailure/>
                    </Switch3>
                    <Condition ID="Workstation_is_timeout" behavior="{behavior}" timeout="{timeout}"/>
                </ReactiveFallback>
                <AlwaysSuccess name="不需要执行子流程"/>
                <RetryUntilSuccessful num_attempts="-1">
                    <IfThenElse>
                        <Condition ID="Is_need_start" behavior="{behavior}" name="流程是否要开始"/>
                        <Sequence name="总流程1是否退出">
                            <Inverter>
                                <Sequence name="开始">
                                    <RetryUntilSuccessful num_attempts="3">
                                        <Action ID="Service_call" data="1" service_name="{robot_service_name}"/>
                                    </RetryUntilSuccessful>
                                    <RetryUntilSuccessful num_attempts="3">
                                        <Action ID="Service_call" data="1" service_name="{workstation_service_name}"/>
                                    </RetryUntilSuccessful>
                                </Sequence>
                            </Inverter>
                            <Sequence name="开始失败，关闭">
                                <ForceSuccess>
                                    <RetryUntilSuccessful num_attempts="2">
                                        <Action ID="Service_call" data="0" service_name="{robot_service_name}"/>
                                    </RetryUntilSuccessful>
                                </ForceSuccess>
                                <ForceSuccess>
                                    <RetryUntilSuccessful num_attempts="2">
                                        <Action ID="Service_call" data="0" service_name="{workstation_service_name}"/>
                                    </RetryUntilSuccessful>
                                </ForceSuccess>
                                <SetBlackboard output_key="ret" value="error"/>
                                <Action ID="Process_return" behavior="{behavior}" ret="{ret}"/>
                            </Sequence>
                        </Sequence>
                        <Sequence name="总流程2是否退出">
                            <Action ID="Get_stop_info" type="{type}"/>
                            <Switch2 case_1="robot" case_2="workstation" name="流程是否有谁需要暂停" variable="{type}">
                                <ForceFailure>
                                    <RetryUntilSuccessful num_attempts="5">
                                        <Action ID="Service_call" data="0" service_name="{robot_service_name}"/>
                                    </RetryUntilSuccessful>
                                </ForceFailure>
                                <ForceFailure>
                                    <RetryUntilSuccessful num_attempts="5">
                                        <Action ID="Service_call" data="0" service_name="{workstation_service_name}"/>
                                    </RetryUntilSuccessful>
                                </ForceFailure>
                                <Sequence name="流程状态">
                                    <ReactiveFallback>
                                        <Condition ID="Is_error_happen" behavior="{behavior}" name="流程是否异常"/>
                                        <Switch3 case_1="add_clean_water" case_2="sewage_water" case_3="rolltube_selfclean" name="不同类别的结束条件" variable="{behavior}">
                                            <Condition ID="Is_clean_water_more" value="1.0"/>
                                            <Condition ID="Is_sewage_water_less" value="0"/>
                                            <ReactiveFallback>
                                                <Condition ID="Is_sewage_water_more" value="1.0"/>
                                                <Condition ID="Is_clean_water_less" value="0"/>
                                            </ReactiveFallback>
                                            <AlwaysFailure/>
                                        </Switch3>
                                        <IfThenElse>
                                            <Condition ID="Workstation_is_timeout" behavior="{behavior}" timeout="{timeout}"/>
                                            <Switch2 case_1="add_clean_water" case_2="sewage_water" variable="{behavior}">
                                                <Action ID="Run_log_pub" application_name="software/workstation" log_type="warn" log_value="机器人加水超时"/>
                                                <Action ID="Run_log_pub" application_name="software/workstation" log_type="warn" log_value="机器人排污超时"/>
                                                <AlwaysSuccess/>
                                            </Switch2>
                                        </IfThenElse>
                                        <Sequence>
                                            <SetBlackboard output_key="ret" value="running"/>
                                            <Action ID="Process_return" behavior="{behavior}" ret="{ret}"/>
                                        </Sequence>
                                    </ReactiveFallback>
                                    <ForceSuccess>
                                        <RetryUntilSuccessful num_attempts="2">
                                            <Action ID="Service_call" data="0" service_name="{robot_service_name}"/>
                                        </RetryUntilSuccessful>
                                    </ForceSuccess>
                                    <ForceSuccess>
                                        <RetryUntilSuccessful num_attempts="2">
                                            <Action ID="Service_call" data="0" service_name="{workstation_service_name}"/>
                                        </RetryUntilSuccessful>
                                    </ForceSuccess>
                                </Sequence>
                            </Switch2>
                        </Sequence>
                    </IfThenElse>
                </RetryUntilSuccessful>
            </IfThenElse>
        </Sequence>
    </BehaviorTree>
    <!-- ////////// -->
    <TreeNodesModel>
        <Action ID="Get_process_type">
            <input_port default="{process}" name="process"/>
        </Action>
        <Action ID="Get_stop_info">
            <input_port name="type"/>
        </Action>
        <Action ID="Get_workstation_info">
            <input_port default="4000" name="clean_capacity">清水箱容积/mL</input_port>
            <output_port default="0" name="clean_fluid"/>
            <input_port default="350" name="clean_fluid_traffic">清水泵流量ml/min</input_port>
        </Action>
        <Condition ID="Is_change_to">
            <input_port name="is_in_pile"/>
        </Condition>
        <Condition ID="Is_clean_water_less">
            <input_port name="value"/>
        </Condition>
        <Condition ID="Is_clean_water_more">
            <input_port name="value"/>
        </Condition>
        <Condition ID="Is_error_happen">
            <input_port default="NULL" name="behavior"/>
        </Condition>
        <Condition ID="Is_need_start">
            <input_port default="{behavior}" name="behavior"/>
        </Condition>
        <Condition ID="Is_sewage_water_less">
            <input_port name="value"/>
        </Condition>
        <Condition ID="Is_sewage_water_more">
            <input_port name="value"/>
        </Condition>
        <Condition ID="Is_timeout">
            <input_port name="hour"/>
            <input_port name="min"/>
            <input_port name="msec"/>
            <input_port name="sec"/>
        </Condition>
        <Action ID="Start_charge">
            <input_port name="data"/>
        </Action>
        <Action ID="Process_init">
            <input_port default="{behavior}" name="behavior"/>
        </Action>
        <Action ID="Process_return">
            <input_port default="{behavior}" name="behavior"/>
            <input_port default="{ret}" name="ret"/>
        </Action>
        <Action ID="Run_log_pub">
            <input_port default="software/workstation" name="application_name"/>
            <input_port name="log_type"/>
            <input_port name="log_value"/>
        </Action>
        <Action ID="Service_call">
            <input_port name="data"/>
            <input_port name="service_name"/>
        </Action>
        <SubTree ID="Sub_process">
            <input_port default="behavior" name="behavior"/>
            <input_port default="robot_service_name" name="robot_service_name"/>
            <input_port default="timeout" name="timeout"/>
            <input_port default="workstation_service_name" name="workstation_service_name"/>
        </SubTree>
        <Action ID="Waiting_charge_state">
            <input_port name="percentage">Use -1 to ignore the judgment condition.</input_port>
            <input_port name="power_supply_status">Use -1 to ignore the judgment condition.</input_port>
            <input_port name="state"/>
        </Action>
        <Action ID="Workstation_blackboard">
            <inout_port default="behavior" name="output_key1"/>
            <inout_port default="robot_service_name" name="output_key2"/>
            <inout_port default="timeout" name="output_key3"/>
            <inout_port default="workstation_service_name" name="output_key4"/>
            <input_port name="value1"/>
            <input_port name="value2"/>
            <input_port name="value3"/>
            <input_port name="value4"/>
        </Action>
        <Condition ID="Workstation_is_timeout">
            <input_port default="{behavior}" name="behavior"/>
            <input_port default="{timeout}" name="timeout"/>
        </Condition>
    </TreeNodesModel>
    <!-- ////////// -->
</root>
